"""
Abid Ansari AI Assistant - Production Ready Version 2.0
Features:
- Cascading API fallback system (Gemini → Mistral → OpenRouter → OpenAI)
- Email-only authentication with Firebase
- Session management with countdown timer
- Optimized for production distribution
- Lazy loading for minimal initial footprint
"""

import sys
import os
import json
import time
import threading
import requests
import ctypes
import hashlib
import platform
import uuid
from datetime import datetime

# PyQt5 imports
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTextEdit, QScrollArea, QFrame,
                            QMessageBox, QSystemTrayIcon, QMenu, QLineEdit, QDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QObject
from PyQt5.QtGui import QFont, QIcon, QPixmap, QPainter, QColor

# Import stealth mode manager
try:
    from stealth_mode import stealth_manager, apply_stealth_mode, create_stealth_messagebox, create_stealth_dialog, get_stealth_window_flags
    STEALTH_MODE_AVAILABLE = True
    print("✅ Stealth mode manager imported successfully")
except ImportError:
    STEALTH_MODE_AVAILABLE = False
    print("⚠️ Stealth mode manager not available - using basic mode")

# Keyboard and screenshot imports
try:
    from pynput import keyboard
    from PIL import ImageGrab
    import pyautogui
except ImportError:
    print("Installing required packages...")
    os.system("pip install pynput pillow pyautogui")
    from pynput import keyboard
    from PIL import ImageGrab
    import pyautogui

# AI imports
try:
    import google.generativeai as genai
    from mistralai.client import MistralClient
    import openai
except ImportError:
    print("Installing AI packages...")
    os.system("pip install google-generativeai mistralai openai")
    import google.generativeai as genai
    from mistralai.client import MistralClient
    import openai

# Firebase imports
try:
    import firebase_admin
    from firebase_admin import credentials, firestore, auth
except ImportError:
    print("Installing Firebase packages...")
    os.system("pip install firebase-admin")
    import firebase_admin
    from firebase_admin import credentials, firestore, auth

# All AI functionality is embedded in this file

# API Keys - Fetched from user profile with lazy loading
API_KEYS = {
    'gemini': None,
    'mistral': None,
    'openrouter': None,
    'openai': None
}

# Production Configuration
PRODUCTION_CONFIG = {
    'app_name': 'Abid Ansari AI Assistant',
    'version': '2.0.0',
    'session_duration': 7200,  # 2 hours in seconds
    'free_time_limit': 300,    # 5 minutes in seconds
    'timer_update_interval': 1000,  # 1 second in milliseconds
    'api_fallback_order': ['gemini', 'mistral', 'openrouter', 'openai'],
    'api_timeout': 30,  # seconds
    'contact_email': '<EMAIL>',
    'contact_whatsapp': '+91 **********'
}

# Firebase Configuration
FIREBASE_CONFIG = ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

# Configuration
DEFAULT_TIME_LIMIT = 300  # 5 minutes in seconds for new users
WHATSAPP_NUMBER = "**********"  # WhatsApp contact number for support

# Error messages
ERROR_MESSAGES = {
    "email_already_exists": "Email already exists",
    "invalid_credentials": "Invalid email or password",
    "device_limit_exceeded": "Device not authorized",
    "invalid_email": "Please enter a valid email address",
    "password_too_short": "Password must be at least 6 characters",
    "passwords_dont_match": "Passwords do not match"
}

# Device ID generation function
def get_device_id():
    """Generate unique device identifier"""
    try:
        # Get hardware info
        system = platform.system()
        machine = platform.machine()
        processor = platform.processor()

        # Create device fingerprint
        device_string = f"{platform.node()}-{system}-{machine}-{processor}"
        device_id = hashlib.sha256(device_string.encode()).hexdigest()[:16]
        return device_id
    except Exception as e:
        print(f"Error generating device ID: {e}")
        # Fallback to MAC address
        return hashlib.sha256(str(uuid.getnode()).encode()).hexdigest()[:16]

# Stealth Configuration - Adjust these values as needed
# You can also modify these in transparency_config.py for easier adjustment
try:
    from transparency_config import RESPONSE_AREA_TRANSPARENCY, CLICK_THROUGH_DURATION
    print("✅ Loaded transparency settings from transparency_config.py")
except ImportError:
    # Fallback values if config file is not found
    RESPONSE_AREA_TRANSPARENCY = 0.3  # 0.1 = very transparent, 0.9 = almost opaque
    CLICK_THROUGH_DURATION = 150  # milliseconds for click-through effect
    print("⚠️ Using default transparency settings (transparency_config.py not found)")

class FirebaseManager:
    """Firebase authentication and database manager"""

    def __init__(self):
        self.app = None
        self.db = None
        self.initialize_firebase()

    def initialize_firebase(self):
        """Initialize Firebase"""
        try:
            # Initialize Firebase Admin SDK
            cred = credentials.Certificate(FIREBASE_CONFIG)
            self.app = firebase_admin.initialize_app(cred)
            self.db = firestore.client()
            print("Firebase initialized successfully")
        except Exception as e:
            print(f"Firebase initialization failed: {e}")
            self.app = None
            self.db = None

    def check_user_exists(self, email):
        """Check if user exists in Firebase"""
        if not self.db:
            return False

        try:
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            return len(query) > 0
        except Exception as e:
            print(f"Error checking user: {e}")
            return False

    def check_device_exists(self, device_id):
        """Check if device already has an account"""
        if not self.db:
            return False

        try:
            users_ref = self.db.collection('users')
            query = users_ref.where('deviceId', '==', device_id).limit(1).get()
            return len(query) > 0
        except Exception as e:
            print(f"Error checking device: {e}")
            return False

    def get_user_data(self, email):
        """Get user data from Firebase"""
        if not self.db:
            return None

        try:
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()

            if len(query) > 0:
                return query[0].to_dict()
            return None
        except Exception as e:
            print(f"Error getting user data: {e}")
            return None

    def create_user(self, email, password, device_id):
        """Create new user in Firebase - Only one account per device allowed"""
        if not self.db:
            return False, "Database not available"

        try:
            # Check if user already exists with this email
            if self.check_user_exists(email):
                return False, ERROR_MESSAGES.get("email_already_exists", "User already exists")

            # Check if device already has an account
            if self.check_device_exists(device_id):
                return False, "This device already has an account. Only one account per device is allowed."

            # Create user document
            user_data = {
                'email': email,
                'password': hashlib.sha256(password.encode()).hexdigest(),
                'deviceId': device_id,
                'timeLimit': DEFAULT_TIME_LIMIT,
                'timeUsage': 0,
                'createdAt': datetime.now().isoformat(),
                'lastLogin': datetime.now().isoformat(),
                'isActive': True
            }

            # Add user to Firestore
            users_ref = self.db.collection('users')
            users_ref.add(user_data)

            print(f"✅ User created: {email}")
            return True, "Account created successfully"
        except Exception as e:
            print(f"❌ Error creating user: {e}")
            return False, f"Error creating account: {e}"

    def authenticate_user(self, email, password, device_id):
        """Authenticate user with email, password and device ID"""
        if not self.db:
            return False, "Database not available", None

        try:
            # Get user data
            user_data = self.get_user_data(email)
            if not user_data:
                return False, ERROR_MESSAGES.get("invalid_credentials", "Invalid credentials"), None

            # Check password
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            if user_data.get('password') != password_hash:
                return False, ERROR_MESSAGES.get("invalid_credentials", "Invalid credentials"), None

            # Check device ID
            if user_data.get('deviceId') != device_id:
                return False, ERROR_MESSAGES.get("device_limit_exceeded", "Device not authorized"), None

            # Check if account is active
            if not user_data.get('isActive', True):
                return False, "Account is deactivated", None

            # Update last login
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            if query:
                doc_ref = query[0].reference
                doc_ref.update({'lastLogin': datetime.now().isoformat()})

            # Load user's API keys
            self.load_user_api_keys(email)

            print(f"✅ User authenticated: {email}")
            return True, "Login successful", user_data
        except Exception as e:
            print(f"❌ Error authenticating user: {e}")
            return False, f"Authentication error: {e}", None

    def update_time_usage(self, email, time_used):
        """Update user's time usage"""
        if not self.db:
            return False

        try:
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            if query:
                doc_ref = query[0].reference
                doc_ref.update({'timeUsage': time_used})
                return True
        except Exception as e:
            print(f"❌ Error updating time usage: {e}")
        return False

    def get_user_api_keys(self, email):
        """Get user's API keys from Firebase"""
        if not self.db:
            return {}

        try:
            user_data = self.get_user_data(email)
            if user_data and 'apiKeys' in user_data:
                return user_data['apiKeys']
            return {}
        except Exception as e:
            print(f"❌ Error getting API keys: {e}")
            return {}

    def load_user_api_keys(self, email):
        """Load API keys from user profile and update global API_KEYS"""
        global API_KEYS
        try:
            user_api_keys = self.get_user_api_keys(email)
            if user_api_keys:
                # Update global API_KEYS with user's keys
                for provider in API_KEYS.keys():
                    if provider in user_api_keys and user_api_keys[provider]:
                        API_KEYS[provider] = user_api_keys[provider]
                        print(f"✅ Loaded {provider.upper()} API key from user profile")
                    else:
                        print(f"⚠️ No {provider.upper()} API key found in user profile")
                return True
            else:
                print("⚠️ No API keys found in user profile")
                return False
        except Exception as e:
            print(f"❌ Error loading user API keys: {e}")
            return False

    def authenticate_user_by_email(self, email):
        """Authenticate user by email only - PRODUCTION VERSION"""
        if not self.db:
            return False, "Database not available", None

        try:
            # Get user data
            user_data = self.get_user_data(email)
            if not user_data:
                return False, f"User not found. Please contact support for registration.\n\nEmail: {PRODUCTION_CONFIG['contact_email']}\nWhatsApp: {PRODUCTION_CONFIG['contact_whatsapp']}", None

            # Check if account is active
            if not user_data.get('isActive', True):
                return False, f"Account is deactivated. Please contact support.\n\nEmail: {PRODUCTION_CONFIG['contact_email']}\nWhatsApp: {PRODUCTION_CONFIG['contact_whatsapp']}", None

            # Update last login
            users_ref = self.db.collection('users')
            query = users_ref.where('email', '==', email).limit(1).get()
            if query:
                doc_ref = query[0].reference
                doc_ref.update({'lastLogin': datetime.now().isoformat()})

            # Load user's API keys
            self.load_user_api_keys(email)

            print(f"✅ User authenticated: {email}")
            return True, "Login successful", user_data
        except Exception as e:
            print(f"❌ Error authenticating user: {e}")
            return False, f"Authentication error: {e}", None

class AIService(QObject):
    """AI service manager with multiple models and signal-based streaming"""

    # Signals for streaming responses
    answer_ready = pyqtSignal(str)  # Signal for complete answer
    answer_chunk = pyqtSignal(str)  # Signal for chunks of the answer
    answer_complete = pyqtSignal()  # Signal for when the answer is complete
    model_in_use = pyqtSignal(str)  # Signal to indicate which AI model is being used

    def __init__(self):
        super().__init__()
        self.setup_ai_clients()

    def setup_ai_clients(self):
        """Setup AI clients with user's API keys"""
        try:
            # Setup Gemini
            if API_KEYS['gemini']:
                genai.configure(api_key=API_KEYS['gemini'])
                print("✅ Gemini API configured with user's key")
            else:
                print("⚠️ Gemini API key not available - please configure in website")

            # Setup Mistral (with vision capabilities)
            try:
                if API_KEYS['mistral']:
                    self.mistral_client = MistralClient(api_key=API_KEYS['mistral'])
                    print("✅ Mistral API configured with user's key")
                else:
                    print("⚠️ Mistral API key not available - please configure in website")
                    self.mistral_client = None
            except Exception as mistral_error:
                print(f"⚠️ Mistral setup error: {mistral_error}")
                self.mistral_client = None

            # Setup OpenAI (new version)
            if API_KEYS['openai']:
                import openai as openai_client
                self.openai_client = openai_client.OpenAI(api_key=API_KEYS['openai'])
                print("✅ OpenAI API configured with user's key")
            else:
                print("⚠️ OpenAI API key not available - please configure in website")

            print("AI clients initialization completed")
        except Exception as e:
            print(f"Error setting up AI clients: {e}")

    def get_answer(self, question, screenshot=None):
        """Get AI response with signal-based streaming (main entry point)"""
        # Start processing in a separate thread to avoid UI blocking
        thread = threading.Thread(target=self._get_ai_response_thread, args=(question, screenshot))
        thread.daemon = True
        thread.start()

    def _get_ai_response_thread(self, question, screenshot=None):
        """Get AI response in separate thread with streaming"""
        try:
            print(f"🔍 Getting AI response for: {question[:50]}...")

            # Try models in production cascading fallback order
            models = PRODUCTION_CONFIG['api_fallback_order']

            for model in models:
                try:
                    print(f"🤖 Trying {model.upper()} AI...")

                    # Emit model in use signal
                    self.model_in_use.emit(f"{model.upper()} AI")

                    response = self._get_response_from_model(model, question, screenshot)
                    if response and response.strip():
                        print(f"✅ {model.upper()} AI responded successfully")

                        # Send response with streaming
                        full_response = f"[{model.upper()}] {response}"
                        self._send_answer_in_chunks(full_response)
                        return
                    else:
                        print(f"❌ {model.upper()} AI returned empty response")
                except Exception as e:
                    print(f"❌ Error with {model.upper()}: {e}")
                    continue

            # Fallback test response for demonstration
            test_response = f"🤖 TEST RESPONSE: I received your question '{question}'. This is a test response to demonstrate the smooth streaming functionality. All AI services are currently unavailable, but the streaming system is working perfectly! You can see how the text appears character by character, just like ChatGPT. This creates a smooth and professional user experience."
            self._send_answer_in_chunks(f"[TEST] {test_response}")

        except Exception as e:
            print(f"❌ Error in AI response thread: {e}")
            error_msg = f"Error getting AI response: {e}"
            self._send_answer_in_chunks(error_msg)

    def _get_response_from_model(self, model, question, screenshot=None):
        """Get response from specific AI model"""
        # Check if this is an image-related question
        if screenshot is not None:
            # Image Analysis Optimized AI Persona
            system_prompt = """You are an expert image analyst and AI assistant. You can analyze any type of image and provide detailed, helpful responses about what you see.

CORE CAPABILITIES:
• Visual Analysis: Describe objects, people, text, layouts, colors, and compositions in images
• Text Recognition: Read and transcribe any text visible in images (OCR capabilities)
• Technical Analysis: Analyze screenshots of code, applications, websites, and technical content
• General Questions: Answer any questions about the image content, context, or details
• Problem Solving: Help with issues shown in screenshots or images

RESPONSE STYLE FOR IMAGE ANALYSIS:
1. Carefully examine the entire image and describe what you see
2. Answer the specific question asked about the image
3. Provide relevant details and context from the image
4. If text is visible, read and include it in your response
5. Be thorough but concise in your analysis
6. Focus on being helpful and informative

ANSWER FORMAT:
• Start by acknowledging what you can see in the image
• Answer the specific question asked
• Provide additional relevant details if helpful
• Be clear and descriptive in your explanations"""

            user_prompt = f"Please analyze this image and answer the following question: {question}\n\nProvide a detailed analysis of what you see and answer the question thoroughly."
        else:
            # Technical Interview-Optimized AI Persona
            system_prompt = """You are a senior full-stack developer with 10+ years of experience across frontend, backend, databases, and DevOps. You excel in technical interviews by providing concise, precise answers that demonstrate deep technical knowledge.

CORE EXPERTISE:
• Frontend: React, Vue, Angular, TypeScript, JavaScript, HTML5, CSS3, responsive design
• Backend: Node.js, Python, Java, C#, Go, microservices, REST/GraphQL APIs
• Databases: SQL (PostgreSQL, MySQL), NoSQL (MongoDB, Redis), database design, optimization
• DevOps: Docker, Kubernetes, CI/CD, AWS/Azure/GCP, monitoring, scaling
• Architecture: System design, design patterns, scalability, security, performance optimization

RESPONSE STYLE FOR TECHNICAL INTERVIEWS:
1. Lead with the most important technical points that directly answer the question
2. Demonstrate problem-solving approach and technical decision-making process
3. Highlight key concepts, best practices, and critical implementation details
4. Provide concise explanations that showcase competency without unnecessary elaboration
5. Focus on practical, real-world application over theoretical concepts
6. Structure answers to impress technical interviewers with depth and precision
7. Sound confident and professional, as expected in senior-level technical interviews

ANSWER FORMAT:
• Start with the core technical solution/answer
• Briefly explain the reasoning behind technical decisions
• Mention relevant best practices or considerations
• Keep responses focused and interview-appropriate (not verbose)
• Demonstrate expertise through precise technical language and insights"""

            user_prompt = f"Technical Question: {question}\n\nProvide a concise, interview-quality response that demonstrates senior full-stack developer expertise."

        if model == 'gemini':
            try:
                if not API_KEYS['gemini']:
                    raise Exception("Gemini API key not configured - please set it in website")

                # Use Gemini 2.0 Flash (latest version optimized for coding and vision)
                model_instance = genai.GenerativeModel(
                    'gemini-2.0-flash-exp',
                    system_instruction=system_prompt
                )

                # Handle image input if screenshot is provided
                if screenshot is not None:
                    # Convert QPixmap to PIL Image for Gemini
                    import io
                    from PyQt5.QtCore import QBuffer, QIODevice
                    from PyQt5.QtGui import QImage
                    from PIL import Image

                    # Convert QPixmap to QImage
                    qimage = screenshot.toImage()

                    # Convert QImage to bytes
                    buffer = QBuffer()
                    buffer.open(QIODevice.WriteOnly)
                    qimage.save(buffer, "PNG")
                    image_bytes = buffer.data().data()

                    # Convert bytes to PIL Image
                    pil_image = Image.open(io.BytesIO(image_bytes))

                    # Generate content with image
                    response = model_instance.generate_content([user_prompt, pil_image])
                else:
                    # Text-only generation
                    response = model_instance.generate_content(user_prompt)

                if response and hasattr(response, 'text') and response.text:
                    return response.text.strip()
                else:
                    raise Exception("Gemini returned empty or invalid response")
            except Exception as e:
                raise Exception(f"Gemini API error: {e}")

        elif model == 'mistral':
            try:
                if not API_KEYS['mistral']:
                    raise Exception("Mistral API key not configured - please set it in website")

                if not hasattr(self, 'mistral_client') or self.mistral_client is None:
                    raise Exception("Mistral client not initialized")

                # Handle image input if screenshot is provided
                if screenshot is not None:
                    # Convert QPixmap to base64 for Mistral vision
                    import base64
                    import io
                    from PyQt5.QtCore import QBuffer, QIODevice
                    from PyQt5.QtGui import QImage

                    # Convert QPixmap to QImage
                    qimage = screenshot.toImage()

                    # Convert QImage to bytes
                    buffer = QBuffer()
                    buffer.open(QIODevice.WriteOnly)
                    qimage.save(buffer, "PNG")
                    image_bytes = buffer.data().data()

                    # Convert to base64
                    base64_image = base64.b64encode(image_bytes).decode('utf-8')

                    # Use vision-capable model with image
                    messages = [
                        {"role": "system", "content": system_prompt},
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": user_prompt},
                                {"type": "image_url", "image_url": f"data:image/png;base64,{base64_image}"}
                            ]
                        }
                    ]
                    model_name = "pixtral-12b-latest"  # Use vision model
                else:
                    # Text-only messages
                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                    model_name = "mistral-large-2407"  # Use text model

                # Use Mistral with appropriate model
                response = self.mistral_client.chat(
                    model=model_name,
                    messages=messages
                )
                if response and hasattr(response, 'choices') and response.choices:
                    content = response.choices[0].message.content
                    if content:
                        return content.strip()
                raise Exception("Mistral returned empty response")
            except Exception as e:
                raise Exception(f"Mistral API error: {e}")

        elif model == 'openrouter':
            try:
                if not API_KEYS['openrouter']:
                    raise Exception("OpenRouter API key not configured - please set it in website")

                headers = {
                    "Authorization": f"Bearer {API_KEYS['openrouter']}",
                    "Content-Type": "application/json"
                }
                data = {
                    "model": "anthropic/claude-3-sonnet",
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ]
                }
                response = requests.post("https://openrouter.ai/api/v1/chat/completions",
                                       headers=headers, json=data, timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if 'choices' in result and result['choices']:
                        content = result['choices'][0]['message']['content']
                        if content:
                            return content.strip()
                    raise Exception("OpenRouter returned empty response")
                else:
                    raise Exception(f"OpenRouter API error: {response.status_code} - {response.text}")
            except Exception as e:
                raise Exception(f"OpenRouter API error: {e}")

        elif model == 'openai':
            try:
                if not API_KEYS['openai']:
                    raise Exception("OpenAI API key not configured - please set it in website")

                if hasattr(self, 'openai_client'):
                    response = self.openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        timeout=30
                    )
                    if response and response.choices:
                        content = response.choices[0].message.content
                        if content:
                            return content.strip()
                    raise Exception("OpenAI returned empty response")
                else:
                    raise Exception("OpenAI client not initialized")
            except Exception as e:
                raise Exception(f"OpenAI API error: {e}")

        return None

    def _send_answer_in_chunks(self, answer):
        """Send answer in chunks for smooth streaming display"""
        try:
            # First emit the complete answer as backup
            self.answer_ready.emit(answer)

            # Split into chunks for streaming
            chunk_size = 15  # Characters per chunk
            delay_ms = 50  # Milliseconds between chunks

            # Split text into chunks
            chunks = []
            for i in range(0, len(answer), chunk_size):
                chunks.append(answer[:i + chunk_size])

            # Send chunks with delay using thread-safe method
            import time
            for i, chunk in enumerate(chunks):
                if i > 0:
                    time.sleep(delay_ms / 1000.0)  # Convert to seconds
                self.answer_chunk.emit(chunk)

            # Signal completion
            self.answer_complete.emit()

        except Exception as e:
            print(f"❌ Error in _send_answer_in_chunks: {e}")
            # Fallback: send complete answer immediately
            self.answer_chunk.emit(answer)
            self.answer_complete.emit()

class KeyboardSignals(QObject):
    """Signals for keyboard events"""
    caps_lock_signal = pyqtSignal()
    arrow_key_signal = pyqtSignal(str)  # direction
    screenshot_signal = pyqtSignal()
    display_screenshot_signal = pyqtSignal(object)  # QPixmap
    submit_question_signal = pyqtSignal(str)  # question text
    clear_text_signal = pyqtSignal()
    delete_char_signal = pyqtSignal()
    add_newline_signal = pyqtSignal()
    add_char_signal = pyqtSignal(str)

class ResponseStreamer(QObject):
    """Handles smooth streaming of AI responses like ChatGPT"""
    chunk_signal = pyqtSignal(str)  # For streaming text chunks
    finished_signal = pyqtSignal()  # When streaming is complete

    def __init__(self):
        super().__init__()
        self.timer = QTimer()
        self.timer.timeout.connect(self.stream_next_chunk)
        self.current_text = ""
        self.current_position = 0
        self.chunk_size = 3  # Characters per chunk
        self.delay_ms = 30  # Milliseconds between chunks

    def start_streaming(self, text):
        """Start streaming the given text"""
        self.current_text = text
        self.current_position = 0
        self.timer.start(self.delay_ms)

    def stream_next_chunk(self):
        """Stream the next chunk of text"""
        if self.current_position >= len(self.current_text):
            self.timer.stop()
            self.finished_signal.emit()
            return

        # Get next chunk
        end_pos = min(self.current_position + self.chunk_size, len(self.current_text))
        chunk = self.current_text[self.current_position:end_pos]

        # Emit the chunk
        self.chunk_signal.emit(chunk)

        # Update position
        self.current_position = end_pos

    def stop_streaming(self):
        """Stop the streaming"""
        self.timer.stop()

class MainPopup(QWidget):
    """Main popup window with all functionality"""

    def __init__(self):
        super().__init__()
        self.firebase_manager = FirebaseManager()

        # Initialize embedded AI service with signal-based streaming
        self.ai_service = AIService()
        print("✅ Using embedded AI service with signal-based streaming")

        self.current_user = None
        self.keyboard_listener = None
        self.caps_lock_pressed = False
        self.shift_pressed = False
        self.alt_pressed = False
        self.ctrl_pressed = False
        self.pressed_keys = set()  # Track currently pressed keys
        self.response_history = []
        self.current_response_index = -1

        # Track first-time usage for better focus handling
        self.first_caps_lock_use = True
        self.first_alt_use = True

        # Screenshot state variables
        self.current_screenshot_path = None
        self.current_screenshot_data = None  # Store PIL image data
        self.current_screenshot_pixmap = None  # Store QPixmap for AI service

        # Create keyboard signals
        self.keyboard_signals = KeyboardSignals()
        self.keyboard_signals.caps_lock_signal.connect(self.handle_caps_lock_press)
        self.keyboard_signals.arrow_key_signal.connect(self.handle_arrow_key)
        self.keyboard_signals.screenshot_signal.connect(self.take_screenshot)
        self.keyboard_signals.display_screenshot_signal.connect(self.display_screenshot_in_ui_memory)
        self.keyboard_signals.submit_question_signal.connect(self._submit_question_from_signal)
        self.keyboard_signals.clear_text_signal.connect(self._clear_all_text)
        self.keyboard_signals.delete_char_signal.connect(self._delete_one_character)
        self.keyboard_signals.add_newline_signal.connect(self._add_new_line)
        self.keyboard_signals.add_char_signal.connect(self._add_character_to_input)

        # Create response streamer for smooth text display
        self.response_streamer = ResponseStreamer()
        self.response_streamer.chunk_signal.connect(self.append_response_chunk)
        self.response_streamer.finished_signal.connect(self.on_streaming_finished)

        # Streaming state
        self.current_streaming_response = ""
        self.is_streaming = False

        # Screenshot state
        self.current_screenshot_path = None
        self.screenshot_widget = None

        self.setup_ui()
        self.setup_ai_service_connections()  # Connect AI service signals
        self.setup_keyboard_listener()

        # Apply comprehensive stealth mode
        self.apply_full_stealth_mode()

        # SIMPLE FIX: Show main window directly, no separate login dialog
        self.show()
        self.ensure_user_visibility()
        print("🔐 Main window shown - login required to use functionality")

        # Start with no user logged in
        self.current_user = None
        self.update_ui_for_login_state()

    def setup_ui(self):
        """Setup the main UI with stealth mode"""
        self.setWindowTitle("Abid Ansari AI Assistant")

        # FIXED: Apply stealth mode window flags with user visibility preserved
        if STEALTH_MODE_AVAILABLE:
            stealth_flags = get_stealth_window_flags()
            self.setWindowFlags(stealth_flags)
            print("🥷 Applied FIXED stealth window flags (user visibility preserved)")
        else:
            self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
            print("👁️ Applied normal window flags")

        self.setAttribute(Qt.WA_TranslucentBackground)

        # FIXED: Removed aggressive stealth attributes that break input functionality
        # The main stealth protection comes from Windows API (SetWindowDisplayAffinity)
        # NOT from Qt attributes that make the window unusable
        print("🥷 Stealth attributes: Using Windows API only (preserving Qt functionality)")

        # Set window size and position
        self.setFixedSize(800, 600)
        self.move(100, 100)

        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Header with time display
        self.header = QLabel("🤖 Abid Ansari AI Assistant")
        self.header.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                          stop:0 #2196F3, stop:1 #1976D2);
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)
        self.header.setAlignment(Qt.AlignCenter)

        # Status label (keep for compatibility but hide it)
        self.status_label = QLabel("Press Caps Lock to ask a question")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(76, 175, 80, 200);
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setVisible(False)  # Hide the status label

        # Question input field (hidden by default) - FIXED for visibility
        self.question_input = QTextEdit()
        self.question_input.setStyleSheet("""
            QTextEdit {
                background-color: #FFFFFF;
                color: #000000;
                font-size: 16px;
                font-weight: bold;
                font-family: 'Segoe UI', sans-serif;
                border: 4px solid #2196F3;
                border-radius: 12px;
                padding: 15px;
                selection-background-color: #2196F3;
                selection-color: white;
                opacity: 1.0;
            }
            QTextEdit:focus {
                border: 4px solid #1976D2;
                background-color: #F8F9FA;
                color: #000000;
                opacity: 1.0;
            }
        """)
        self.question_input.setPlaceholderText("🎤 Type your question here...")
        self.question_input.setMaximumHeight(120)
        self.question_input.setMinimumHeight(80)
        self.question_input.setVisible(False)  # Hidden by default

        # CRITICAL FIX: Ensure input field is always fully visible and functional
        self.question_input.setWindowOpacity(1.0)  # Force full opacity
        self.question_input.setAttribute(Qt.WA_OpaquePaintEvent)  # Ensure opaque painting

        # CRITICAL: Ensure input field works properly
        self.question_input.setAttribute(Qt.WA_TransparentForMouseEvents, False)
        self.question_input.setFocusPolicy(Qt.StrongFocus)  # Allow strong focus

        # CRITICAL FIX: Add Enter and Ctrl+Backspace key handling
        def handle_input_keys(event):
            from PyQt5.QtCore import Qt
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                if not (event.modifiers() & Qt.ShiftModifier):  # Enter without Shift
                    question = self.question_input.toPlainText().strip()
                    if question:
                        self.submit_question(question)
                    else:
                        # CRITICAL FIX: If no question typed but Enter pressed, remove screenshot
                        if hasattr(self, 'screenshot_frame') and self.screenshot_frame.isVisible():
                            print("🗑️ Enter pressed with no question - removing screenshot")
                            self.delete_screenshot()
                    return
            elif event.key() == Qt.Key_Backspace and event.modifiers() & Qt.ControlModifier:
                # Ctrl+Backspace: Clear all text
                self.question_input.clear()
                print("🗑️ Ctrl+Backspace: Question input cleared")
                return
            # Let Qt handle all other keys naturally
            QTextEdit.keyPressEvent(self.question_input, event)

        self.question_input.keyPressEvent = handle_input_keys
        print("🔧 Input field configured with Enter and Ctrl+Backspace handling")

        self.is_input_mode = False

        # Screenshot display area (hidden by default)
        self.screenshot_frame = QFrame()
        self.screenshot_frame.setStyleSheet("""
            QFrame {
                background-color: #F5F5F5;
                border: 2px solid #2196F3;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        self.screenshot_frame.setVisible(False)

        screenshot_layout = QVBoxLayout()

        # Screenshot header
        self.screenshot_header = QLabel("📸 Screenshot Captured - Type your question about the image:")
        self.screenshot_header.setStyleSheet("""
            QLabel {
                color: #1976D2;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            }
        """)

        # Screenshot image display
        self.screenshot_label = QLabel()
        self.screenshot_label.setAlignment(Qt.AlignCenter)
        self.screenshot_label.setStyleSheet("""
            QLabel {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                background-color: white;
                min-height: 200px;
                max-height: 300px;
            }
        """)
        self.screenshot_label.setScaledContents(True)

        # Screenshot controls
        screenshot_controls = QHBoxLayout()

        self.delete_screenshot_btn = QPushButton("🗑️ Delete Image")
        self.delete_screenshot_btn.clicked.connect(self.delete_screenshot)
        self.delete_screenshot_btn.setToolTip("Click to remove the screenshot and clear the image")
        self.delete_screenshot_btn.setStyleSheet("""
            QPushButton {
                background-color: #F44336;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 8px 15px;
                border: none;
                border-radius: 5px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
        """)

        screenshot_controls.addWidget(self.delete_screenshot_btn)
        screenshot_controls.addStretch()

        screenshot_layout.addWidget(self.screenshot_header)
        screenshot_layout.addWidget(self.screenshot_label)
        screenshot_layout.addLayout(screenshot_controls)

        self.screenshot_frame.setLayout(screenshot_layout)

        # Response area - FULLY VISIBLE to user
        self.response_area = QTextEdit()
        self.response_area.setStyleSheet("""
            QTextEdit {
                background-color: #1a1a1a;
                color: #FFFFFF;
                font-size: 14px;
                font-family: 'Segoe UI', sans-serif;
                border: 2px solid #4CAF50;
                border-radius: 10px;
                padding: 15px;
                line-height: 1.6;
                selection-background-color: #4CAF50;
                selection-color: #FFFFFF;
            }
            QTextEdit:hover {
                border: 2px solid #45a049;
            }
            QTextEdit:focus {
                outline: none;
                border: 2px solid #45a049;
            }
            QScrollBar:vertical {
                background-color: #2b2b2b;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #4CAF50;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #45a049;
            }
        """)
        self.response_area.setPlaceholderText("💬 AI responses will appear here with smooth streaming...")
        self.response_area.setReadOnly(True)

        # Stealth configuration - prevent cursor changes and blinking
        self.response_area.setFocusPolicy(Qt.NoFocus)  # Prevent focus to avoid blinking
        self.response_area.setTextInteractionFlags(Qt.NoTextInteraction)  # Disable text interaction

        # Disable cursor blinking completely
        self.response_area.setCursorWidth(0)  # Hide cursor completely
        self.response_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.response_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Prevent any selection or editing
        self.response_area.setTextInteractionFlags(Qt.NoTextInteraction)
        self.response_area.setContextMenuPolicy(Qt.NoContextMenu)  # Disable right-click menu

        # FIXED: Make window COMPLETELY visible to user - NO transparency at all
        # Stealth protection comes ONLY from Windows API (SetWindowDisplayAffinity)
        # User ko bilkul clear dikhna chahiye - input fields must be visible
        self.setWindowOpacity(1.0)  # 100% visible to user
        self.setAttribute(Qt.WA_OpaquePaintEvent)  # Ensure opaque painting
        print("👁️ Window set to 100% visible for user (FIXED for input visibility)")

        # Additional stealth settings - make response area completely non-interactive
        self.response_area.setEnabled(True)  # Keep enabled but override events
        self.response_area.setAcceptDrops(False)  # No drag and drop

        # Force arrow cursor permanently - this is the key fix
        self.response_area.setCursor(Qt.ArrowCursor)
        self.response_area.viewport().setCursor(Qt.ArrowCursor)  # Also set on viewport

        # Override all mouse and keyboard events to prevent any interaction
        self.response_area.keyPressEvent = lambda event: event.ignore()
        self.response_area.keyReleaseEvent = lambda event: event.ignore()
        self.response_area.focusInEvent = lambda event: self.response_area.clearFocus()
        self.response_area.focusOutEvent = lambda event: None

        # Remove transparent attribute - we need to handle mouse events to implement click-through
        # self.response_area.setAttribute(Qt.WA_TransparentForMouseEvents, True)

        # Override mouse events for click-through functionality
        self.response_area.mousePressEvent = self.response_area_mouse_press_event
        self.response_area.mouseReleaseEvent = self.response_area_mouse_release_event
        self.response_area.mouseMoveEvent = self.response_area_mouse_move_event

        # Override enter/leave events to maintain cursor behavior
        self.response_area.enterEvent = self.response_area_enter_event
        self.response_area.leaveEvent = self.response_area_leave_event

        # Set up a timer to periodically enforce cursor behavior
        self.cursor_timer = QTimer()
        self.cursor_timer.timeout.connect(self.enforce_cursor_behavior)
        self.cursor_timer.start(100)  # Check every 100ms to reduce overhead

        # CRITICAL: Set up a timer to ensure user visibility is maintained
        self.visibility_timer = QTimer()
        self.visibility_timer.timeout.connect(self.check_user_visibility)
        self.visibility_timer.start(5000)  # Check every 5 seconds

        # Control buttons - FIXED with login buttons
        button_layout = QHBoxLayout()

        # Left side - Authentication buttons
        self.login_button = QPushButton("🔐 Login")
        self.login_button.clicked.connect(self.show_login_popup)

        self.create_account_button = QPushButton("🆕 Create Account")
        self.create_account_button.clicked.connect(self.show_create_account_popup)

        # Right side - App functionality buttons
        self.clear_button = QPushButton("🗑️ Clear")
        self.clear_button.clicked.connect(self.clear_response)

        self.logout_button = QPushButton("🚪 Logout")
        self.logout_button.clicked.connect(self.logout_user)

        self.hide_button = QPushButton("👁️ Hide")
        self.hide_button.clicked.connect(self.hide)

        self.close_button = QPushButton("❌ Close")
        self.close_button.clicked.connect(self.close_application)

        # Style buttons
        button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 10px 15px;
                border: none;
                border-radius: 5px;
                margin: 2px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #45a049, stop:1 #3d8b40);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #3d8b40, stop:1 #2e7d32);
            }
        """

        # Apply style to all buttons
        for button in [self.login_button, self.create_account_button, self.clear_button,
                      self.logout_button, self.hide_button, self.close_button]:
            button.setStyleSheet(button_style)

        # Add buttons to layout - left side auth, right side functionality
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.create_account_button)
        button_layout.addStretch()  # Space between auth and functionality buttons
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.logout_button)
        button_layout.addWidget(self.hide_button)
        button_layout.addWidget(self.close_button)

        # Add widgets to layout
        layout.addWidget(self.header)
        layout.addWidget(self.status_label)
        layout.addWidget(self.question_input)  # Add question input
        layout.addWidget(self.screenshot_frame)  # Add screenshot frame
        layout.addWidget(self.response_area)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Apply main window styling - CLEARLY VISIBLE
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                          stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 15px;
                border: 3px solid #4CAF50;
            }
        """)
        print("🎨 Applied visible window styling")

    def setup_ai_service_connections(self):
        """Setup connections to AI service signals for streaming responses"""
        try:
            # Connect AI service signals to UI update methods
            self.ai_service.answer_ready.connect(self.on_answer_ready)
            self.ai_service.answer_chunk.connect(self.on_answer_chunk)
            self.ai_service.answer_complete.connect(self.on_answer_complete)
            self.ai_service.model_in_use.connect(self.on_model_in_use)
            print("✅ AI service signals connected successfully")
        except Exception as e:
            print(f"⚠️ Error connecting AI service signals: {e}")

    def update_ui_for_login_state(self):
        """Update UI based on login state"""
        if self.current_user:
            # User is logged in - show functionality, hide auth buttons
            self.header.setText("🤖 Abid Ansari AI Assistant - ✅ Authenticated")
            self.login_button.setVisible(False)
            self.create_account_button.setVisible(False)
            self.logout_button.setVisible(True)
            self.clear_button.setEnabled(True)
            self.hide_button.setEnabled(True)
            self.response_area.setEnabled(True)

            # CRITICAL FIX: Clear the login message and show welcome message
            self.response_area.clear()
            welcome_message = f"""
🎉 Welcome back! You are now logged in.

👤 User: {self.current_user.get('email', 'Unknown')}
⏰ Time remaining: {self.get_remaining_time_display()}

🚀 Ready to use AI Assistant!
💡 Press Caps Lock to ask questions
🎯 Use Alt key for screenshots
            """
            self.response_area.setText(welcome_message.strip())
            print("✅ User authenticated - functionality enabled and welcome message shown")
        else:
            # User not logged in - show auth buttons, disable functionality
            self.header.setText("🤖 Abid Ansari AI Assistant - 🔐 Login Required")
            self.login_button.setVisible(True)
            self.create_account_button.setVisible(True)
            self.logout_button.setVisible(False)
            self.clear_button.setEnabled(False)
            self.hide_button.setEnabled(False)
            self.response_area.setEnabled(False)
            self.response_area.setText("🔐 Please login to use the AI Assistant")
            print("🔐 Login required - functionality disabled")

    def show_login_popup(self):
        """Show login popup on top of main window"""
        login_dialog = LoginDialog(self.firebase_manager)
        login_dialog.setParent(self)  # Set main window as parent
        print("🔐 Showing login popup")

        if login_dialog.exec_() == QDialog.Accepted:
            self.current_user = login_dialog.user_data
            self.start_time_tracking()
            self.update_ui_for_login_state()

    def show_create_account_popup(self):
        """Show create account popup on top of main window"""
        create_dialog = CreateAccountDialog(self.firebase_manager)
        create_dialog.setParent(self)  # Set main window as parent
        print("🆕 Showing create account popup")

        if create_dialog.exec_() == QDialog.Accepted:
            self.current_user = create_dialog.user_data
            self.start_time_tracking()
            self.update_ui_for_login_state()

    def submit_question(self, question):
        """Submit question for AI processing"""
        if not self.current_user:
            print("❌ Cannot submit question - user not authenticated")
            return

        print(f"✅ Submitting question: {question}")

        # Check if this is a screenshot question and handle accordingly
        is_screenshot_question = (hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None) or \
                               (hasattr(self, 'current_screenshot_path') and self.current_screenshot_path is not None)

        # Clear and hide input
        self.question_input.clear()
        self.question_input.setVisible(False)
        self.is_input_mode = False

        # Process the question
        if is_screenshot_question:
            print("📸 Processing screenshot question")
            # Check if this is a screenshot question (memory-based)
            if hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None:
                print(f"📸 Processing screenshot question from memory: {question}")
                self.process_screenshot_question_memory(question)
            elif self.current_screenshot_path and os.path.exists(self.current_screenshot_path):
                print(f"📸 Processing screenshot question from file: {question}")
                self.process_screenshot_question(question, self.current_screenshot_path)

            # CRITICAL FIX: Always clear screenshot after processing
            print("🗑️ Auto-clearing screenshot after question submission")
            QTimer.singleShot(100, self.delete_screenshot)  # Small delay to ensure processing starts
        else:
            # Process regular question
            self.process_question(question)

    def get_remaining_time_display(self):
        """Get remaining time as display string"""
        try:
            if not self.current_user:
                return "Not logged in"

            time_limit = self.current_user.get('timeLimit', 300)  # Default 5 minutes
            time_usage = self.current_user.get('timeUsage', 0)
            remaining_seconds = max(0, time_limit - time_usage)

            minutes = remaining_seconds // 60
            seconds = remaining_seconds % 60

            return f"{minutes}m {seconds}s"
        except Exception as e:
            print(f"Error getting remaining time: {e}")
            return "Unknown"

    def start_time_tracking(self):
        """Start time tracking for the user - PRODUCTION VERSION"""
        if not self.current_user:
            return

        self.time_used = self.current_user.get('timeUsage', 0)
        self.time_limit = self.current_user.get('timeLimit', PRODUCTION_CONFIG['free_time_limit'])

        # Create timer for countdown with production interval
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time_display)
        self.time_timer.start(PRODUCTION_CONFIG['timer_update_interval'])  # Use production config

        print(f"⏰ Time tracking started - Used: {self.time_used}s, Limit: {self.time_limit}s")

        # Show initial time display
        self.update_time_display()

    def update_time_display(self):
        """Update time display and check for expiration - PRODUCTION VERSION"""
        if not self.current_user:
            return

        self.time_used += 1
        remaining_time = self.time_limit - self.time_used

        if remaining_time <= 0:
            self.show_time_expired_popup()
            return

        # Update Firebase every 10 seconds
        if self.time_used % 10 == 0:
            self.firebase_manager.update_time_usage(self.current_user['email'], self.time_used)

        # Format time display
        hours = remaining_time // 3600
        minutes = (remaining_time % 3600) // 60
        seconds = remaining_time % 60

        if hours > 0:
            time_text = f"⏰ {hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            time_text = f"⏰ {minutes:02d}:{seconds:02d}"

        # Determine color based on remaining time
        if remaining_time > 300:  # More than 5 minutes
            color = "#4CAF50"  # Green
        elif remaining_time > 60:  # More than 1 minute
            color = "#FF9800"  # Orange
        else:  # Less than 1 minute
            color = "#F44336"  # Red

        # Update header with prominent time display
        app_title = f"{PRODUCTION_CONFIG['app_name']} v{PRODUCTION_CONFIG['version']}"
        self.header.setText(f'<span style="color: {color}; font-weight: bold; font-size: 16px; float: left;">{time_text}</span><span style="text-align: center; width: 100%;">🤖 {app_title}</span>')
        self.header.setTextFormat(1)  # Set to RichText format

    def show_time_expired_popup(self):
        """Show time expired popup with WhatsApp contact (stealth mode) - FIXED styling"""
        self.time_timer.stop()

        # Create normal message box (stealth will be applied after)
        msg = QMessageBox(self)
        msg.setWindowTitle("⏰ Time Expired")
        msg.setText("Your time has expired. Please contact support for more access.")
        msg.setIcon(QMessageBox.Warning)

        # FIXED: Remove icon border and make message white for visibility
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #2C3E50;
                border: none;
                border-radius: 10px;
                padding: 20px;
            }
            QMessageBox QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 15px;
                border: none;
                background-color: transparent;
            }
            QMessageBox QLabel#qt_msgbox_label {
                color: white;
                font-size: 16px;
                font-weight: bold;
                background-color: transparent;
                border: none;
            }
            QMessageBox QLabel#qt_msgboxex_icon_label {
                border: none;
                background-color: transparent;
                padding: 0px;
                margin: 0px;
            }
            QMessageBox QPushButton {
                background-color: #4CAF50;
                color: white;
                font-size: 12px;
                font-weight: bold;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                margin: 5px;
                min-width: 120px;
            }
            QMessageBox QPushButton:hover {
                background-color: #45a049;
            }
            QMessageBox QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)

        # Apply stealth after UI setup to preserve styling
        if STEALTH_MODE_AVAILABLE:
            try:
                stealth_manager.hide_from_screen_capture(msg)
                stealth_manager.hide_from_taskbar(msg)
                print("🥷 Applied stealth to time expired popup (UI preserved)")
            except Exception as e:
                print(f"❌ Error applying stealth to popup: {e}")

        # Add contact buttons with production info
        whatsapp_btn = msg.addButton("📱 WhatsApp Support", QMessageBox.ActionRole)
        email_btn = msg.addButton("📧 Email Support", QMessageBox.ActionRole)
        close_btn = msg.addButton("❌ Close", QMessageBox.RejectRole)

        msg.exec_()

        if msg.clickedButton() == whatsapp_btn:
            import webbrowser
            whatsapp_number = PRODUCTION_CONFIG['contact_whatsapp'].replace('+', '')
            whatsapp_url = f"https://wa.me/{whatsapp_number}?text=Hi, my session has expired in {PRODUCTION_CONFIG['app_name']}. Please help me extend my access."
            webbrowser.open(whatsapp_url)
        elif msg.clickedButton() == email_btn:
            import webbrowser
            email_url = f"mailto:{PRODUCTION_CONFIG['contact_email']}?subject=Session Expired - {PRODUCTION_CONFIG['app_name']}&body=Hi, my session has expired. Please help me extend my access."
            webbrowser.open(email_url)

        # Disable functionality
        self.current_user = None
        self.header.setText("🤖 Abid Ansari AI Assistant - ❌ Time Expired")
        self.header.setTextFormat(0)  # Reset to plain text

    def get_caps_lock_state(self):
        """Get current Caps Lock state using Windows API"""
        try:
            import ctypes
            return ctypes.windll.user32.GetKeyState(0x14) & 0x0001 != 0
        except Exception as e:
            print(f"Error getting Caps Lock state: {e}")
            return False

    def setup_keyboard_listener(self):
        """Setup global keyboard listener"""
        try:
            self.keyboard_listener = keyboard.Listener(
                on_press=self.on_key_press,
                on_release=self.on_key_release
            )
            self.keyboard_listener.start()
            print("Keyboard listener started")
        except Exception as e:
            print(f"Error starting keyboard listener: {e}")

    def on_key_press(self, key):
        """Handle key press events"""
        try:
            print(f"Key pressed: {key}")  # Debug output

            # Initialize pressed_keys set if it doesn't exist
            if not hasattr(self, 'pressed_keys'):
                self.pressed_keys = set()

            # Add key to pressed keys set
            self.pressed_keys.add(key)

            # Shift detection
            if key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = True
                print("Shift key pressed")

            # Alt detection (including alt_gr for international keyboards)
            elif key in [keyboard.Key.alt, keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr]:
                self.alt_pressed = True
                print(f"Alt key pressed: {key}")

            # Ctrl detection
            elif key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = True
                print("Ctrl key pressed")

            # Ctrl+Space detection - toggle window visibility
            elif key == keyboard.Key.space and self.ctrl_pressed:
                print("🔄 Ctrl+Space pressed - Toggling window visibility")
                QTimer.singleShot(0, self.toggle_visibility)

            # Caps Lock detection - trigger AI question and auto-populate copied text
            elif key == keyboard.Key.caps_lock:
                if self.current_user:
                    print("🔒 Caps Lock pressed - Handling caps lock")
                    # Use signal to handle caps lock in main thread
                    self.keyboard_signals.caps_lock_signal.emit()

            # Arrow keys with Shift modifier - Move window
            elif key == keyboard.Key.up and self.shift_pressed:
                if self.current_user:
                    print("⬆️ Shift + Up pressed - Moving window up")
                    self.keyboard_signals.arrow_key_signal.emit('up')

            elif key == keyboard.Key.down and self.shift_pressed:
                if self.current_user:
                    print("⬇️ Shift + Down pressed - Moving window down")
                    self.keyboard_signals.arrow_key_signal.emit('down')

            elif key == keyboard.Key.left and self.shift_pressed:
                if self.current_user:
                    print("⬅️ Shift + Left pressed - Moving window left")
                    self.keyboard_signals.arrow_key_signal.emit('left')

            elif key == keyboard.Key.right and self.shift_pressed:
                if self.current_user:
                    print("➡️ Shift + Right pressed - Moving window right")
                    self.keyboard_signals.arrow_key_signal.emit('right')

            # Handle Delete key for screenshot removal (works globally, not just in input mode)
            elif key == keyboard.Key.delete:
                if self.current_user:
                    print("🗑️ Delete key pressed - checking for screenshot...")
                    # Check if screenshot exists (UI or memory)
                    has_screenshot = False

                    if hasattr(self, 'screenshot_frame') and self.screenshot_frame.isVisible():
                        has_screenshot = True
                        print("🗑️ Screenshot visible in UI - removing")
                    elif (hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None):
                        has_screenshot = True
                        print("🗑️ Screenshot data found in memory - removing")
                    elif (hasattr(self, 'current_screenshot_pixmap') and self.current_screenshot_pixmap is not None):
                        has_screenshot = True
                        print("🗑️ Screenshot pixmap found in memory - removing")
                    elif (hasattr(self, 'current_screenshot_path') and self.current_screenshot_path is not None):
                        has_screenshot = True
                        print("🗑️ Screenshot path found - removing")

                    if has_screenshot:
                        print("🗑️ DELETING screenshot from UI and memory")
                        # Use signal to handle in main thread
                        QTimer.singleShot(0, self.delete_screenshot)
                    else:
                        print("🗑️ Delete key pressed but no screenshot to remove")

            # Handle Enter key for screenshot removal when NOT in input mode
            elif key == keyboard.Key.enter and not self.is_input_mode:
                if self.current_user:
                    # Check if screenshot exists and remove it
                    has_screenshot = False

                    if hasattr(self, 'screenshot_frame') and self.screenshot_frame.isVisible():
                        has_screenshot = True
                        print("🗑️ Enter pressed - Screenshot visible in UI - removing")
                    elif (hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None):
                        has_screenshot = True
                        print("🗑️ Enter pressed - Screenshot data found in memory - removing")
                    elif (hasattr(self, 'current_screenshot_pixmap') and self.current_screenshot_pixmap is not None):
                        has_screenshot = True
                        print("🗑️ Enter pressed - Screenshot pixmap found in memory - removing")

                    if has_screenshot:
                        print("🗑️ ENTER KEY: Deleting screenshot from UI and memory")
                        QTimer.singleShot(0, self.delete_screenshot)

            # CRITICAL FIX: Completely ignore ALL keys when input mode is active
            # This prevents ANY interference with typing
            elif self.is_input_mode and self.question_input.isVisible():
                # COMPLETELY DISABLED: No keyboard handling during input mode
                # Qt handles everything naturally without any interference
                return  # Exit immediately, don't process any keys

        except Exception as e:
            print(f"Error in key press: {e}")

    def on_key_release(self, key):
        """Handle key release events"""
        try:
            # Remove key from pressed keys set
            if hasattr(self, 'pressed_keys') and key in self.pressed_keys:
                self.pressed_keys.remove(key)

            # Check if Alt key was released and trigger screenshot
            if key in [keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr] and self.alt_pressed:
                if self.current_user:
                    print("📸 Alt key released - Taking screenshot")
                    # Use signal to ensure proper thread handling
                    self.keyboard_signals.screenshot_signal.emit()

            # Update key state flags
            if key == keyboard.Key.caps_lock:
                self.caps_lock_pressed = False
            elif key == keyboard.Key.shift or key == keyboard.Key.shift_r:
                self.shift_pressed = False
            elif key in [keyboard.Key.alt, keyboard.Key.alt_l, keyboard.Key.alt_r, keyboard.Key.alt_gr]:
                self.alt_pressed = False
                print(f"Alt key released: {key}")
            elif key == keyboard.Key.ctrl or key == keyboard.Key.ctrl_r:
                self.ctrl_pressed = False
                print("Ctrl key released")
        except Exception as e:
            print(f"Error in key release: {e}")

    def handle_caps_lock_press(self):
        """Handle Caps Lock press - toggle input mode"""
        if not self.current_user:
            print("❌ Access denied - User not authenticated")
            return

        # Use QTimer with single shot to handle caps lock with delay in main thread
        QTimer.singleShot(100, self._handle_caps_lock_delayed)



    def _handle_caps_lock_delayed(self):
        """Handle Caps Lock press with delay for state update"""
        try:
            # Check current Caps Lock state
            caps_lock_state = self.get_caps_lock_state()

            print(f"🔒 Caps Lock state: {caps_lock_state}")

            if caps_lock_state:  # Caps Lock is ON
                print("🔒 Caps Lock is ON - Showing question input")

                # Show and focus the main window
                self.show()
                self.raise_()
                self.activateWindow()

                # Show input field
                self.is_input_mode = True
                self.question_input.setVisible(True)
                self.question_input.clear()

                # Auto-populate with copied text from clipboard
                self._auto_populate_clipboard_text()

                self.question_input.setPlaceholderText("🎤 Type your question here and press Enter...")

                # Force focus with multiple attempts
                self._force_input_focus_aggressive()

                # Keep the time display in header, no need to change status

            else:  # Caps Lock is OFF
                print("🔒 Caps Lock is OFF - Hiding question input")

                # Hide input field
                self.is_input_mode = False
                self.question_input.setVisible(False)

                # Keep the time display in header, no need to change status

        except Exception as e:
            print(f"❌ Error handling caps lock press: {e}")

    def _force_input_focus(self):
        """Force focus on input field with aggressive approach"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                # Make sure window is active and on top
                self.setWindowState(self.windowState() & ~Qt.WindowMinimized | Qt.WindowActive)
                self.activateWindow()
                self.raise_()
                self.show()

                # Clear any existing focus
                QApplication.setActiveWindow(self)

                # Force focus on input field with different methods
                self.question_input.setFocus(Qt.OtherFocusReason)
                self.question_input.setFocus(Qt.MouseFocusReason)
                self.question_input.setFocus(Qt.TabFocusReason)
                self.question_input.setFocus(Qt.ActiveWindowFocusReason)

                # Move cursor to end and ensure visibility
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.End)
                self.question_input.setTextCursor(cursor)
                self.question_input.ensureCursorVisible()

                # Force repaint
                self.question_input.repaint()
                self.question_input.update()

                print("🔒 Applied focus methods")
        except Exception as e:
            print(f"❌ Error forcing input focus: {e}")

    def _force_input_focus_aggressive(self):
        """Even more aggressive focus approach for Caps Lock - FIRST TIME FIX"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                print(f"🔒 Starting aggressive focus (first_caps_lock_use: {self.first_caps_lock_use})...")

                # STEP 1: Make window completely active and visible
                self.setWindowState(Qt.WindowNoState)
                self.show()
                self.raise_()
                self.activateWindow()
                QApplication.setActiveWindow(self)
                QApplication.processEvents()

                # STEP 2: Use a more direct approach - simulate a mouse click on the input field
                if self.first_caps_lock_use or self.first_alt_use:
                    print("🔒 First-time use - using direct click simulation...")

                    # Get the center point of the input field
                    input_rect = self.question_input.geometry()
                    center_x = input_rect.x() + input_rect.width() // 2
                    center_y = input_rect.y() + input_rect.height() // 2

                    # Convert to global coordinates
                    global_point = self.mapToGlobal(self.question_input.pos())
                    global_center_x = global_point.x() + input_rect.width() // 2
                    global_center_y = global_point.y() + input_rect.height() // 2

                    # Simulate mouse click on the input field using Windows API
                    try:
                        import ctypes
                        from ctypes import wintypes

                        # Get current cursor position to restore later
                        current_pos = wintypes.POINT()
                        ctypes.windll.user32.GetCursorPos(ctypes.byref(current_pos))

                        # Move cursor to input field center and click
                        ctypes.windll.user32.SetCursorPos(global_center_x, global_center_y)
                        ctypes.windll.user32.mouse_event(0x0002, 0, 0, 0, 0)  # Left button down
                        ctypes.windll.user32.mouse_event(0x0004, 0, 0, 0, 0)  # Left button up

                        # Restore cursor position
                        ctypes.windll.user32.SetCursorPos(current_pos.x, current_pos.y)

                        print(f"🔒 Simulated click at ({global_center_x}, {global_center_y})")

                    except Exception as click_error:
                        print(f"⚠️ Click simulation failed: {click_error}")

                    # Process events after click
                    QApplication.processEvents()

                    # Mark first use as complete
                    if self.first_caps_lock_use:
                        self.first_caps_lock_use = False
                        print("🔒 First-time Caps Lock use completed")
                    if self.first_alt_use:
                        self.first_alt_use = False
                        print("📸 First-time Alt use completed")

                # STEP 3: Standard focus methods
                self.question_input.setFocus(Qt.MouseFocusReason)
                self.question_input.setFocus(Qt.ActiveWindowFocusReason)
                QApplication.processEvents()

                # STEP 4: Multiple delayed focus attempts
                QTimer.singleShot(50, self._force_input_focus)
                QTimer.singleShot(150, self._force_input_focus)
                QTimer.singleShot(300, self._force_input_focus)

                # STEP 5: Ensure cursor visibility
                QTimer.singleShot(200, self._ensure_cursor_visible)

                print("🔒 Applied aggressive focus methods with click simulation")
        except Exception as e:
            print(f"❌ Error in aggressive focus: {e}")

    def _ensure_cursor_visible(self):
        """Ensure cursor is visible and input is ready"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.End)
                self.question_input.setTextCursor(cursor)
                self.question_input.ensureCursorVisible()
                self.question_input.setFocus(Qt.OtherFocusReason)
                print("🔒 Ensured cursor visibility and final focus")
        except Exception as e:
            print(f"❌ Error ensuring cursor visibility: {e}")



    def _auto_populate_clipboard_text(self):
        """Auto-populate input field with clipboard text when Caps Lock is pressed"""
        try:
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard_text = clipboard.text()

            if clipboard_text and clipboard_text.strip():
                self.question_input.setPlainText(clipboard_text.strip())
                print(f"📋 Auto-populated clipboard text: {clipboard_text[:50]}...")

                # Move cursor to end
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.End)
                self.question_input.setTextCursor(cursor)
            else:
                print("📋 No text in clipboard to auto-populate")

        except Exception as e:
            print(f"❌ Error auto-populating clipboard text: {e}")

    def _clear_all_text(self):
        """Clear all text from input field (Ctrl+Backspace)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                current_text = self.question_input.toPlainText()
                self.question_input.clear()
                print(f"🗑️ Cleared all text (Ctrl+Backspace) - was: '{current_text[:50]}...'")

                # Move cursor to beginning
                cursor = self.question_input.textCursor()
                cursor.movePosition(cursor.Start)
                self.question_input.setTextCursor(cursor)
            else:
                print("❌ Cannot clear text - input mode not active or input not visible")
        except Exception as e:
            print(f"❌ Error clearing all text: {e}")

    def _delete_one_character(self):
        """Delete one character from input field (Backspace)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                if not cursor.hasSelection():
                    cursor.deletePreviousChar()
                else:
                    cursor.removeSelectedText()
                print("⌫ Deleted one character")
        except Exception as e:
            print(f"❌ Error deleting character: {e}")

    def _add_new_line(self):
        """Add new line to input field (Shift+Enter)"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                cursor = self.question_input.textCursor()
                cursor.insertText("\n")
                print("↵ Added new line (Shift+Enter)")
        except Exception as e:
            print(f"❌ Error adding new line: {e}")

    def _submit_current_question(self):
        """Submit current question (Enter) - this runs in main thread"""
        try:
            if self.is_input_mode and self.question_input.isVisible():
                question = self.question_input.toPlainText().strip()
                if question:
                    print(f"✅ Submitting question: {question}")
                    self._submit_question_from_signal(question)
                else:
                    print("❌ Empty question, not submitting")
        except Exception as e:
            print(f"❌ Error submitting question: {e}")

    def _submit_question_from_signal(self, question):
        """Handle question submission from signal (runs in main thread)"""
        try:
            print(f"✅ Processing question in main thread: {question}")

            # Clear and hide input immediately
            self.question_input.clear()
            self.question_input.setVisible(False)
            self.is_input_mode = False

            # Update status to show processing
            self.status_label.setText("🤔 Processing your question...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(255, 152, 0, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

            # Check if this is a screenshot question (memory-based)
            if hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None:
                print(f"📸 Processing screenshot question: {question}")
                self.process_screenshot_question_memory(question)
                # AUTOMATICALLY clear screenshot after processing
                print("🗑️ Auto-clearing screenshot after question submission")
                self.delete_screenshot()
            else:
                # Process regular question
                self.process_question(question)
        except Exception as e:
            print(f"❌ Error processing question from signal: {e}")

    def _add_character_to_input(self, char):
        """Add character to input field - DISABLED to prevent duplicates"""
        try:
            # DISABLED: Qt input field handles all character input automatically
            # This prevents duplicate characters from keyboard listener
            # All typing is now handled by Qt's native input handling
            pass
        except Exception as e:
            print(f"❌ Error in character handler: {e}")

    def handle_input_key_press(self, event):
        """Handle key press in input field"""
        from PyQt5.QtCore import Qt
        from PyQt5.QtWidgets import QTextEdit

        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            # Check if Shift is pressed for new line
            if event.modifiers() & Qt.ShiftModifier:
                # Shift+Enter adds new line
                QTextEdit.keyPressEvent(self.question_input, event)
            else:
                # Enter submits question
                question = self.question_input.toPlainText().strip()
                if question:
                    print(f"✅ Submitting question: {question}")

                    # Clear and hide input immediately
                    self.question_input.clear()
                    self.question_input.setVisible(False)
                    self.is_input_mode = False

                    # Update status to show processing
                    self.status_label.setText("🤔 Processing your question...")
                    self.status_label.setStyleSheet("""
                        QLabel {
                            background-color: rgba(255, 152, 0, 200);
                            color: white;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 15px;
                            border-radius: 8px;
                            margin-bottom: 10px;
                        }
                    """)

                    # Check if this is a screenshot question
                    if self.current_screenshot_path and os.path.exists(self.current_screenshot_path):
                        print(f"📸 Processing screenshot question: {question}")
                        self.process_screenshot_question(question, self.current_screenshot_path)
                        # AUTOMATICALLY clear screenshot after processing
                        print("🗑️ Auto-clearing screenshot after question submission")
                        self.delete_screenshot()
                    elif hasattr(self, 'current_screenshot_data') and self.current_screenshot_data is not None:
                        print(f"📸 Processing screenshot question from memory: {question}")
                        self.process_screenshot_question_memory(question)
                        # AUTOMATICALLY clear screenshot after processing
                        print("🗑️ Auto-clearing screenshot after question submission")
                        self.delete_screenshot()
                    else:
                        # Process regular question
                        self.process_question(question)
                else:
                    print("❌ Empty question, not submitting")
                return
        else:
            # Handle other keys normally
            QTextEdit.keyPressEvent(self.question_input, event)



    def process_question(self, question):
        """Process question and get AI response"""
        if not self.current_user:
            return

        self.status_label.setText("🤔 Thinking...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(255, 152, 0, 200);
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)

        # Store current question for display
        self.current_question = question
        self.current_timestamp = datetime.now().strftime("%H:%M:%S")

        # Use embedded AI service with streaming
        print("🔍 Using embedded AI service with streaming...")
        self.ai_service.get_answer(question)

    def _get_ai_response_thread(self, question):
        """Get AI response in separate thread"""
        try:
            print(f"🔍 Getting AI response for: {question[:50]}...")
            response = self.ai_service.get_ai_response(question)
            print(f"🔍 Got AI response: {response[:100]}...")

            # Update UI in main thread
            self.display_response(question, response)  # Call directly to avoid threading issues
        except Exception as e:
            print(f"❌ Error in AI response thread: {e}")
            error_msg = f"Error getting AI response: {e}"
            self.display_response(question, error_msg)  # Call directly to avoid threading issues

    def display_response(self, question, response):
        """Display AI response in the UI with smooth streaming"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")

            # Make sure response area is visible and enabled
            self.response_area.setVisible(True)
            self.response_area.setEnabled(True)
            self.response_area.setReadOnly(False)

            # Add question header in plain text with formatting
            header_text = f"\n{'='*60}\n[{timestamp}] 🤔 Question:\n{question}\n\n💡 AI Response:\n"

            # Get current content and add new content
            current_content = self.response_area.toPlainText()
            new_content = current_content + header_text + response + f"\n{'='*60}\n"

            # Set the complete content
            self.response_area.setPlainText(new_content)
            self.response_area.setReadOnly(True)

            # Auto-scroll to bottom
            scrollbar = self.response_area.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Update status to show complete
            self.status_label.setText("✅ Response Complete")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(76, 175, 80, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

            # Reset status immediately (no QTimer to avoid threading issues)
            self.reset_status_to_ready()

        except Exception as e:
            print(f"❌ Error in display_response: {e}")
            # Fallback method
            try:
                self.response_area.setReadOnly(False)
                self.response_area.append(f"\n[{datetime.now().strftime('%H:%M:%S')}] Question: {question}\n\nResponse: {response}\n{'='*60}\n")
                self.response_area.setReadOnly(True)
            except Exception as e2:
                print(f"❌ Error in fallback display: {e2}")

    def append_response_chunk(self, chunk):
        """Append a chunk of response text with cursor indicator like original"""
        try:
            if not self.is_streaming:
                print("🔍 Not streaming, skipping chunk")
                return

            print(f"🔍 Appending chunk: '{chunk}' (length: {len(chunk)})")
            self.current_streaming_response += chunk

            # Move cursor to end and insert text with cursor indicator
            cursor = self.response_area.textCursor()
            cursor.movePosition(cursor.End)

            # Remove any existing cursor indicator
            text = self.response_area.toPlainText()
            if text.endswith("▏"):
                cursor.movePosition(cursor.End)
                cursor.deletePreviousChar()

            # Insert the new chunk with cursor indicator
            cursor.insertText(chunk + "▏")
            print(f"🔍 Inserted chunk into response area")

            # Auto-scroll to bottom
            scrollbar = self.response_area.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Force UI update
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

        except Exception as e:
            print(f"❌ Error in append_response_chunk: {e}")

    def on_streaming_finished(self):
        """Called when streaming is complete"""
        if not self.is_streaming:
            return

        self.is_streaming = False

        # Remove the cursor indicator
        cursor = self.response_area.textCursor()
        cursor.movePosition(cursor.End)
        text = self.response_area.toPlainText()
        if text.endswith("▏"):
            cursor.deletePreviousChar()

        # Add closing separator
        cursor.insertText(f"\n{'='*60}\n")

        # Add to response history
        formatted_response = f"[{self.current_timestamp}] Question: {self.current_question}\n\nResponse: {self.full_response}"

        response_data = {
            'question': self.current_question,
            'response': self.full_response,
            'timestamp': self.current_timestamp,
            'formatted_response': formatted_response
        }
        self.response_history.append(response_data)
        self.current_response_index = len(self.response_history) - 1

        # Auto-scroll to bottom
        scrollbar = self.response_area.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # Reset status
        self.reset_status_to_ready()

    def handle_arrow_key(self, direction):
        """Handle Shift + Arrow key combinations - Move window position"""
        if not self.current_user:
            return

        try:
            # Show window if hidden
            if not self.isVisible():
                self.show()
                self.raise_()
                self.activateWindow()

            # Get current window position
            current_pos = self.pos()
            move_distance = 30  # pixels to move (increased for better visibility)

            # Get screen geometry to prevent moving window off-screen
            screen = QApplication.desktop().screenGeometry()
            window_size = self.size()

            if direction == 'up':
                # Move window up (ensure it doesn't go above screen)
                new_y = max(0, current_pos.y() - move_distance)
                new_pos = (current_pos.x(), new_y)
                self.move(new_pos[0], new_pos[1])
                self.status_label.setText("⬆️ Window Moved Up")
                print(f"⬆️ Moved window up to position: {new_pos}")

            elif direction == 'down':
                # Move window down (ensure it doesn't go below screen)
                new_y = min(screen.height() - window_size.height(), current_pos.y() + move_distance)
                new_pos = (current_pos.x(), new_y)
                self.move(new_pos[0], new_pos[1])
                self.status_label.setText("⬇️ Window Moved Down")
                print(f"⬇️ Moved window down to position: {new_pos}")

            elif direction == 'left':
                # Move window left (ensure it doesn't go off left edge)
                new_x = max(0, current_pos.x() - move_distance)
                new_pos = (new_x, current_pos.y())
                self.move(new_pos[0], new_pos[1])
                self.status_label.setText("⬅️ Window Moved Left")
                print(f"⬅️ Moved window left to position: {new_pos}")

            elif direction == 'right':
                # Move window right (ensure it doesn't go off right edge)
                new_x = min(screen.width() - window_size.width(), current_pos.x() + move_distance)
                new_pos = (new_x, current_pos.y())
                self.move(new_pos[0], new_pos[1])
                self.status_label.setText("➡️ Window Moved Right")
                print(f"➡️ Moved window right to position: {new_pos}")

            # Update status styling for movement feedback
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(156, 39, 176, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

            # Reset status immediately (no QTimer to avoid threading issues)
            self.reset_status_to_ready()

        except Exception as e:
            print(f"❌ Error handling arrow key {direction}: {e}")

    def show_response_from_history(self):
        """Show a specific response from history"""
        if 0 <= self.current_response_index < len(self.response_history):
            response_data = self.response_history[self.current_response_index]
            self.response_area.clear()
            self.response_area.append(response_data['formatted_response'])

    def reset_status_to_ready(self):
        """Reset status label to ready state"""
        if self.current_user:
            self.status_label.setText("✅ Ready - Press Caps Lock to ask questions")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(76, 175, 80, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)
        else:
            self.status_label.setText("❌ Access Denied - User not authenticated")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(244, 67, 54, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

    # AI Service Signal Handlers
    def on_answer_ready(self, answer):
        """Handle complete answer from AI service (backup)"""
        print(f"📝 Full answer received: {answer[:100]}...")
        # This is a backup - the chunked version should handle display

    def on_answer_chunk(self, accumulated_text):
        """Handle answer chunk from AI service for streaming display"""
        try:
            print(f"📝 Received accumulated text: {accumulated_text[:50]}...")

            # Initialize streaming if not already started
            if not self.is_streaming:
                self.is_streaming = True

                # Clear response area completely for latest response only
                self.response_area.setReadOnly(False)
                self.response_area.clear()  # Clear all previous content

                # Add only current question header
                header_text = f"[{self.current_timestamp}] 🤔 Question: {self.current_question}\n\n💡 AI Response:\n"
                self.response_area.setPlainText(header_text)

                # Store the starting position for response content
                self.response_start_position = len(self.response_area.toPlainText())

            # Update the response area with the accumulated text
            cursor = self.response_area.textCursor()

            # Move to the start of the response content
            cursor.setPosition(self.response_start_position)

            # Select all text from response start to end and replace it
            cursor.movePosition(cursor.End, cursor.KeepAnchor)
            cursor.removeSelectedText()

            # Insert the accumulated text with cursor indicator
            cursor.insertText(accumulated_text + "▏")

            # Store the full response for history
            self.full_response = accumulated_text

            # Auto-scroll to bottom
            scrollbar = self.response_area.verticalScrollBar()
            scrollbar.setValue(scrollbar.maximum())

            # Force UI update
            QApplication.processEvents()

        except Exception as e:
            print(f"❌ Error in on_answer_chunk: {e}")

    def on_answer_complete(self):
        """Handle completion of answer streaming"""
        try:
            print("✅ Answer streaming complete")

            if self.is_streaming:
                self.is_streaming = False

                # Remove cursor indicator only
                cursor = self.response_area.textCursor()
                cursor.movePosition(cursor.End)
                text = self.response_area.toPlainText()
                if text.endswith("▏"):
                    cursor.deletePreviousChar()

                # No separators - keep it clean for latest response only

                # Make read-only
                self.response_area.setReadOnly(True)

                # Store in history but don't display old responses
                if hasattr(self, 'current_question') and hasattr(self, 'full_response'):
                    response_data = {
                        'question': self.current_question,
                        'response': self.full_response,
                        'timestamp': self.current_timestamp,
                    }
                    self.response_history.append(response_data)
                    self.current_response_index = len(self.response_history) - 1

                # Auto-scroll to bottom
                scrollbar = self.response_area.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

            # Update status to show complete
            self.status_label.setText("✅ Response Complete")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(76, 175, 80, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

            # Reset status immediately (no QTimer to avoid threading issues)
            self.reset_status_to_ready()

        except Exception as e:
            print(f"❌ Error in on_answer_complete: {e}")

    def on_model_in_use(self, model_name):
        """Handle model in use signal"""
        print(f"🤖 Using AI model: {model_name}")
        self.status_label.setText(f"🤖 {model_name} is responding...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: rgba(33, 150, 243, 200);
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)

    def take_screenshot(self):
        """Take 300x300px screenshot around cursor and display in UI"""
        if not self.current_user:
            print("❌ Access denied - User not authenticated")
            return

        try:
            print("📸 Starting cursor-based screenshot process...")

            # Update status
            self.status_label.setText("📸 Capturing Screenshot Around Cursor...")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(156, 39, 176, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

            # Get cursor position
            cursor_pos = pyautogui.position()
            print(f"📍 Cursor position: {cursor_pos}")

            # Calculate screenshot area (300x300 around cursor)
            size = 300
            left = max(0, cursor_pos.x - size // 2)
            top = max(0, cursor_pos.y - size // 2)
            right = left + size
            bottom = top + size

            # Hide window temporarily for clean screenshot
            was_visible = self.isVisible()
            if was_visible:
                self.hide()
                time.sleep(0.3)  # Quick hide

            # Take screenshot of the area around cursor
            print(f"📸 Capturing area: ({left}, {top}, {right}, {bottom})")
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))

            # Show window again if it was visible
            if was_visible:
                self.show()
                self.raise_()
                self.activateWindow()

            # Store screenshot in memory ONLY (NO FILE SAVING)
            import io
            img_buffer = io.BytesIO()
            screenshot.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # Create QPixmap from buffer
            pixmap = QPixmap()
            pixmap.loadFromData(img_buffer.getvalue())

            # Store the screenshot data for AI analysis (in memory only)
            self.current_screenshot_data = screenshot
            self.current_screenshot_pixmap = pixmap
            self.current_screenshot_path = None  # No file path - memory only

            print("📸 Screenshot captured in memory only (no file saved)")

            # Display screenshot in UI using signal (ensures main thread)
            self.keyboard_signals.display_screenshot_signal.emit(pixmap)

            # Update status
            self.status_label.setText("� Screenshot Ready - Type your question about the image")
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(33, 150, 243, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)

        except Exception as e:
            error_msg = f"❌ Screenshot Failed: {str(e)}"
            self.status_label.setText(error_msg)
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: rgba(244, 67, 54, 200);
                    color: white;
                    font-size: 14px;
                    font-weight: bold;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 10px;
                }
            """)
            print(f"❌ Screenshot error: {e}")

            # Reset status immediately (no QTimer to avoid threading issues)
            self.reset_status_to_ready()

    def display_screenshot_in_ui(self, screenshot_path):
        """Display the captured screenshot in the UI"""
        try:
            # Store the current screenshot path
            self.current_screenshot_path = screenshot_path

            # Load and display the image
            pixmap = QPixmap(screenshot_path)
            if not pixmap.isNull():
                # Scale the image to fit the display area while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.screenshot_label.setPixmap(scaled_pixmap)

                # Show the screenshot frame
                self.screenshot_frame.setVisible(True)

                # Show input field for question about the image
                self.is_input_mode = True
                self.question_input.setVisible(True)
                self.question_input.clear()
                self.question_input.setPlaceholderText("🖼️ Ask a question about this image and press Enter...")

                # Force focus on input field immediately
                self._force_input_focus()

                print(f"📸 Screenshot displayed in UI: {screenshot_path}")
            else:
                print(f"❌ Failed to load screenshot: {screenshot_path}")

        except Exception as e:
            print(f"❌ Error displaying screenshot: {e}")

    def display_screenshot_in_ui_memory(self, pixmap):
        """Display the captured screenshot in the UI from memory (no file)"""
        try:
            # Clear any existing screenshot path since we're using memory
            self.current_screenshot_path = None

            # Display the image directly from pixmap
            if not pixmap.isNull():
                # Scale the image to fit the display area while maintaining aspect ratio
                scaled_pixmap = pixmap.scaled(300, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.screenshot_label.setPixmap(scaled_pixmap)

                # Show the screenshot frame
                self.screenshot_frame.setVisible(True)

                # Show input field for question about the image
                self.is_input_mode = True
                self.question_input.setVisible(True)
                self.question_input.clear()
                self.question_input.setPlaceholderText("🖼️ Ask a question about this image and press Enter...")

                # Force focus on input field with first-time Alt handling
                if self.first_alt_use:
                    print("📸 First-time Alt use - applying extra focus techniques...")
                    self._force_input_focus_aggressive()
                    self.first_alt_use = False
                    print("📸 First-time Alt use completed")
                else:
                    self._force_input_focus_aggressive()

                print("📸 Screenshot displayed in UI from memory (no file)")
            else:
                print("❌ Failed to load screenshot from memory")

        except Exception as e:
            print(f"❌ Error displaying screenshot from memory: {e}")



    def delete_screenshot(self):
        """Delete the current screenshot and hide the display IMMEDIATELY from UI and memory"""
        try:
            print("🗑️ DELETING screenshot from UI and memory...")

            # STEP 1: IMMEDIATELY hide the screenshot frame and clear image from UI
            if hasattr(self, 'screenshot_frame'):
                self.screenshot_frame.setVisible(False)
                self.screenshot_frame.hide()  # Double ensure it's hidden
                print("🗑️ Screenshot frame hidden from UI")

            if hasattr(self, 'screenshot_label'):
                self.screenshot_label.clear()
                self.screenshot_label.setPixmap(QPixmap())  # Clear pixmap completely
                self.screenshot_label.setText("")  # Clear any text too
                print("🗑️ Screenshot label cleared from UI")

            # STEP 2: Force immediate UI update
            QApplication.processEvents()
            self.update()  # Force widget update
            print("🗑️ UI updated immediately")

            # STEP 3: Delete the file if it exists
            if hasattr(self, 'current_screenshot_path') and self.current_screenshot_path and os.path.exists(self.current_screenshot_path):
                try:
                    os.remove(self.current_screenshot_path)
                    print(f"🗑️ Deleted screenshot file: {self.current_screenshot_path}")
                except Exception as file_error:
                    print(f"⚠️ Could not delete file {self.current_screenshot_path}: {file_error}")

            # STEP 4: Clear ALL screenshot-related data from memory
            self.current_screenshot_path = None
            self.current_screenshot_data = None
            self.current_screenshot_pixmap = None
            print("🗑️ ALL screenshot data cleared from memory")

            # STEP 5: Hide input field if it was shown for screenshot
            if hasattr(self, 'is_input_mode') and self.is_input_mode:
                if hasattr(self, 'question_input'):
                    self.question_input.setVisible(False)
                    self.question_input.clear()  # Clear any text in input field
                self.is_input_mode = False
                print("🗑️ Input field hidden and cleared")

            # STEP 6: Reset status if needed
            if hasattr(self, 'status_label'):
                self.status_label.setText("")
                self.status_label.setStyleSheet("")  # Clear any special styling

            # STEP 7: Force final UI update to ensure everything is cleared
            QApplication.processEvents()
            self.repaint()  # Force complete repaint

            print("✅ Screenshot COMPLETELY deleted from UI and memory")

        except Exception as e:
            print(f"❌ Error deleting screenshot: {e}")
            # Emergency cleanup
            try:
                if hasattr(self, 'screenshot_frame'):
                    self.screenshot_frame.setVisible(False)
                if hasattr(self, 'is_input_mode'):
                    self.is_input_mode = False
                if hasattr(self, 'question_input'):
                    self.question_input.setVisible(False)
            except:
                pass

    def process_screenshot_question(self, question, screenshot_path):
        """Process screenshot analysis question with vision AI"""
        try:
            # Try to analyze the screenshot with vision-capable AI models
            vision_response = self.get_vision_ai_response(question, screenshot_path)
            self.display_response(f"📸 Screenshot Analysis: {question}", vision_response)
        except Exception as e:
            error_response = f"Screenshot saved as {screenshot_path}. Error analyzing image: {e}"
            self.display_response(f"📸 Screenshot Analysis: {question}", error_response)

    def process_screenshot_question_memory(self, question):
        """Process screenshot analysis question with vision AI using memory-only screenshot"""
        try:
            # Store current question for display
            self.current_question = f"📸 Screenshot Analysis: {question}"
            self.current_timestamp = datetime.now().strftime("%H:%M:%S")

            # Use embedded AI service with streaming for screenshot analysis
            print("🔍 Using embedded AI service with streaming for screenshot analysis...")

            # Convert PIL image to QPixmap for AI service
            if hasattr(self, 'current_screenshot_pixmap') and self.current_screenshot_pixmap:
                self.ai_service.get_answer(question, self.current_screenshot_pixmap)
            else:
                # Fallback: convert PIL to QPixmap
                import io
                from PyQt5.QtGui import QPixmap
                img_buffer = io.BytesIO()
                self.current_screenshot_data.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                pixmap = QPixmap()
                pixmap.loadFromData(img_buffer.getvalue())
                self.ai_service.get_answer(question, pixmap)

            # Screenshot will be auto-deleted after question submission
            print("📸 Screenshot analysis complete - will auto-delete after submission")

        except Exception as e:
            error_response = f"Error analyzing screenshot from memory: {e}"
            # Store current question for display
            self.current_question = f"📸 Screenshot Analysis: {question}"
            self.current_timestamp = datetime.now().strftime("%H:%M:%S")
            # Use the streaming display method
            self.ai_service._send_answer_in_chunks(error_response)
            # Don't auto-delete on error - let user manually delete
            print("❌ Screenshot analysis error - use delete button to remove image")

    def get_vision_ai_response(self, question, image_path):
        """Get response from vision-capable AI models for image analysis"""
        models = ['gemini', 'mistral']  # Only vision-capable models

        for model in models:
            try:
                print(f"🔍 Trying {model.upper()} Vision AI...")
                response = self._get_vision_response_from_model(model, question, image_path)
                if response and response.strip():
                    print(f"✅ {model.upper()} Vision AI responded successfully")
                    return f"[{model.upper()} VISION] {response}"
                else:
                    print(f"❌ {model.upper()} Vision AI returned empty response")
            except Exception as e:
                print(f"❌ Error with {model.upper()} Vision: {e}")
                continue

        return f"❌ Vision analysis failed. Screenshot saved as {image_path}. Please check your API keys."

    def _get_vision_response_from_model(self, model, question, image_path):
        """Get vision response from specific AI model"""
        import base64

        # Read and encode the image
        try:
            with open(image_path, 'rb') as image_file:
                image_data = base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            raise Exception(f"Failed to read image file: {e}")

        if model == 'gemini':
            try:
                # Use Gemini 2.0 Flash with vision
                model_instance = genai.GenerativeModel('gemini-2.0-flash-exp')

                # Create image part
                from PIL import Image
                image = Image.open(image_path)

                prompt = f"Analyze this screenshot and answer: {question}\n\nPlease provide a detailed description of what you see in the image and answer the question based on the visual content."

                response = model_instance.generate_content([prompt, image])
                if response and hasattr(response, 'text') and response.text:
                    return response.text.strip()
                else:
                    raise Exception("Gemini Vision returned empty response")
            except Exception as e:
                raise Exception(f"Gemini Vision API error: {e}")

        elif model == 'mistral':
            try:
                if not hasattr(self, 'mistral_client') or self.mistral_client is None:
                    raise Exception("Mistral client not initialized")

                # Use Mistral with vision capabilities
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": f"Analyze this screenshot and answer: {question}\n\nPlease provide a detailed description of what you see in the image."
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_data}"
                                }
                            }
                        ]
                    }
                ]

                response = self.mistral_client.chat(
                    model="pixtral-12b-2409",  # Mistral's vision model
                    messages=messages
                )

                if response and hasattr(response, 'choices') and response.choices:
                    content = response.choices[0].message.content
                    if content:
                        return content.strip()
                raise Exception("Mistral Vision returned empty response")
            except Exception as e:
                raise Exception(f"Mistral Vision API error: {e}")

        return None

    def clear_response(self):
        """Clear the response area"""
        self.response_area.clear()
        # Keep the time display in header, no need to change status

    def logout_user(self):
        """Logout current user and show login dialog"""
        try:
            # Stop time tracking
            if hasattr(self, 'time_timer') and self.time_timer:
                self.time_timer.stop()

            # Clear current user
            self.current_user = None

            # Clear response area
            self.response_area.clear()

            # Hide input field if visible
            if hasattr(self, 'question_input'):
                self.question_input.setVisible(False)
                self.is_input_mode = False

            # Hide screenshot frame if visible
            if hasattr(self, 'screenshot_frame'):
                self.screenshot_frame.setVisible(False)
                self.delete_screenshot()  # Clear any screenshot data

            # Update header to show logged out status
            self.header.setText("🤖 Abid Ansari AI Assistant - 🚪 Logged Out")
            self.header.setTextFormat(0)  # Reset to plain text

            print("🚪 User logged out successfully")

            # FIXED: Clear user data and update UI to show login state
            self.current_user = None
            self.update_ui_for_login_state()

        except Exception as e:
            print(f"❌ Error during logout: {e}")

    def apply_full_stealth_mode(self):
        """Apply comprehensive stealth mode - FIXED to preserve user visibility"""
        try:
            if STEALTH_MODE_AVAILABLE:
                # Apply stealth mode using stealth manager (now fixed)
                apply_stealth_mode(self)
                print("🥷 FIXED stealth mode applied via stealth manager")
            else:
                # Fallback to basic stealth
                self.hide_from_capture()
                self.hide_from_taskbar_basic()
                print("🥷 Basic stealth mode applied")

            # CRITICAL FIX: Always ensure user visibility after stealth mode
            self.ensure_user_visibility()

        except Exception as e:
            print(f"❌ Error applying stealth mode: {e}")
            # Emergency fallback - ensure user can see the application
            self.ensure_user_visibility()

    def ensure_user_visibility(self):
        """CRITICAL: Ensure application is always visible to the user"""
        try:
            # Force full opacity for user
            self.setWindowOpacity(1.0)

            # Ensure window is shown and raised
            self.show()
            self.raise_()
            self.activateWindow()

            # Ensure input field is visible when needed
            if hasattr(self, 'question_input'):
                self.question_input.setWindowOpacity(1.0)

            print("✅ CRITICAL: User visibility ensured - application is visible to you!")

        except Exception as e:
            print(f"❌ CRITICAL ERROR ensuring user visibility: {e}")

    def hide_from_capture(self):
        """Hide window from screen capture (Windows only)"""
        try:
            hwnd = int(self.winId())

            # Set window to be excluded from capture
            WDA_EXCLUDEFROMCAPTURE = 0x00000011
            result = ctypes.windll.user32.SetWindowDisplayAffinity(hwnd, WDA_EXCLUDEFROMCAPTURE)

            if result:
                print("✅ Window hidden from screen capture")
            else:
                print("⚠️ Failed to hide window from screen capture")

        except Exception as e:
            print(f"❌ Error hiding from capture: {e}")

    def hide_from_taskbar_basic(self):
        """Basic method to hide from taskbar"""
        try:
            hwnd = int(self.winId())

            # Windows API constants
            GWL_EXSTYLE = -20
            WS_EX_TOOLWINDOW = 0x00000080
            WS_EX_NOACTIVATE = 0x08000000

            # Get current extended style
            current_style = ctypes.windll.user32.GetWindowLongW(hwnd, GWL_EXSTYLE)

            # Add tool window and no activate styles
            new_style = current_style | WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE

            # Apply new style
            result = ctypes.windll.user32.SetWindowLongW(hwnd, GWL_EXSTYLE, new_style)

            if result:
                print("✅ Window hidden from taskbar")
            else:
                print("⚠️ Failed to hide window from taskbar")

        except Exception as e:
            print(f"❌ Error hiding from taskbar: {e}")

    def close_application(self):
        """Close the application"""
        try:
            # Stop cursor timer
            if hasattr(self, 'cursor_timer') and self.cursor_timer:
                self.cursor_timer.stop()

            # Stop visibility timer
            if hasattr(self, 'visibility_timer') and self.visibility_timer:
                self.visibility_timer.stop()

            # Stop keyboard listener
            if self.keyboard_listener:
                self.keyboard_listener.stop()

            self.close()
            QApplication.quit()
        except Exception as e:
            print(f"Error closing application: {e}")

    def mousePressEvent(self, event):
        """Handle mouse press for window dragging"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        """Handle mouse move for window dragging"""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_position'):
            self.move(event.globalPos() - self.drag_position)
            event.accept()

    def response_area_mouse_press_event(self, event):
        """Handle mouse press events for response area - implement click-through without blinking"""
        try:
            # Ensure no focus is given to response area
            if self.response_area.hasFocus():
                self.response_area.clearFocus()

            # Force arrow cursor
            self.response_area.setCursor(Qt.ArrowCursor)
            self.response_area.viewport().setCursor(Qt.ArrowCursor)

            # Pass the click through to the application behind
            self.pass_click_through_no_blink(event)

            # Completely ignore the event to prevent any default behavior
            event.ignore()
            return
        except Exception as e:
            print(f"❌ Error in response area mouse press: {e}")

    def response_area_mouse_release_event(self, event):
        """Handle mouse release events for response area - maintain stealth"""
        try:
            # Ensure no focus
            if self.response_area.hasFocus():
                self.response_area.clearFocus()

            # Force arrow cursor
            self.response_area.setCursor(Qt.ArrowCursor)
            self.response_area.viewport().setCursor(Qt.ArrowCursor)

            # Completely ignore the event to prevent any default behavior
            event.ignore()
            return
        except Exception as e:
            print(f"❌ Error in response area mouse release: {e}")

    def response_area_mouse_move_event(self, event):
        """Handle mouse move events for response area - maintain stealth cursor"""
        try:
            # Force arrow cursor on both widget and viewport
            self.response_area.setCursor(Qt.ArrowCursor)
            self.response_area.viewport().setCursor(Qt.ArrowCursor)

            # Ensure no focus
            if self.response_area.hasFocus():
                self.response_area.clearFocus()

            # Completely ignore the event to prevent any hover effects
            event.ignore()

        except Exception as e:
            print(f"❌ Error in response area mouse move: {e}")

    def pass_click_through_no_blink(self, event):
        """Pass mouse clicks through to the application behind without blinking or cursor movement"""
        try:
            import ctypes
            from ctypes import wintypes

            # Get the global position of the click
            global_pos = self.response_area.mapToGlobal(event.pos())

            print(f"🖱️ Attempting click-through at position: {global_pos.x()}, {global_pos.y()}")

            # Store current cursor position to restore it later (don't move cursor)
            current_cursor = ctypes.wintypes.POINT()
            ctypes.windll.user32.GetCursorPos(ctypes.byref(current_cursor))

            # Method: Find the window behind us and send click message directly
            # First, temporarily disable our window from receiving mouse events
            our_hwnd = int(self.winId())

            # Use a different approach: Get the window at the position excluding our window
            # by temporarily setting our window to be transparent to hit testing
            GWL_EXSTYLE = -20
            WS_EX_TRANSPARENT = 0x00000020

            # Get current style
            current_style = ctypes.windll.user32.GetWindowLongW(our_hwnd, GWL_EXSTYLE)

            # Make transparent to hit testing temporarily
            ctypes.windll.user32.SetWindowLongW(our_hwnd, GWL_EXSTYLE, current_style | WS_EX_TRANSPARENT)

            # Now find the window behind us
            target_hwnd = ctypes.windll.user32.WindowFromPoint(ctypes.wintypes.POINT(global_pos.x(), global_pos.y()))

            # Restore our window style immediately
            ctypes.windll.user32.SetWindowLongW(our_hwnd, GWL_EXSTYLE, current_style)

            if target_hwnd and target_hwnd != our_hwnd:
                # Convert to client coordinates of target window
                client_point = ctypes.wintypes.POINT(global_pos.x(), global_pos.y())
                ctypes.windll.user32.ScreenToClient(target_hwnd, ctypes.byref(client_point))

                # Create lParam for the message
                lParam = (client_point.y << 16) | (client_point.x & 0xFFFF)

                # Send click messages directly to the target window
                if event.button() == Qt.LeftButton:
                    ctypes.windll.user32.PostMessageW(target_hwnd, 0x0201, 0x0001, lParam)  # WM_LBUTTONDOWN
                    ctypes.windll.user32.PostMessageW(target_hwnd, 0x0202, 0x0000, lParam)  # WM_LBUTTONUP
                    print(f"🖱️ Left click sent to window {target_hwnd} at client pos ({client_point.x}, {client_point.y})")
                elif event.button() == Qt.RightButton:
                    ctypes.windll.user32.PostMessageW(target_hwnd, 0x0204, 0x0002, lParam)  # WM_RBUTTONDOWN
                    ctypes.windll.user32.PostMessageW(target_hwnd, 0x0205, 0x0000, lParam)  # WM_RBUTTONUP
                    print(f"🖱️ Right click sent to window {target_hwnd} at client pos ({client_point.x}, {client_point.y})")
            else:
                print(f"🖱️ No target window found at position {global_pos.x()}, {global_pos.y()}")

        except Exception as e:
            print(f"❌ Error in click-through: {e}")
            # Ensure we restore window style in case of error
            try:
                if 'our_hwnd' in locals() and 'current_style' in locals():
                    ctypes.windll.user32.SetWindowLongW(our_hwnd, GWL_EXSTYLE, current_style)
            except:
                pass

    def pass_click_through(self, event):
        """Legacy method - kept for compatibility but not used"""
        # This method is no longer used to prevent window blinking
        pass

    def _simulate_click_and_restore(self, global_pos, original_event):
        """Simulate click at position and restore window"""
        try:
            import ctypes

            # Simulate mouse click at the position
            if original_event.button() == Qt.LeftButton:
                # Left mouse button down and up
                ctypes.windll.user32.SetCursorPos(global_pos.x(), global_pos.y())
                ctypes.windll.user32.mouse_event(0x0002, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTDOWN
                ctypes.windll.user32.mouse_event(0x0004, 0, 0, 0, 0)  # MOUSEEVENTF_LEFTUP
            elif original_event.button() == Qt.RightButton:
                # Right mouse button down and up
                ctypes.windll.user32.SetCursorPos(global_pos.x(), global_pos.y())
                ctypes.windll.user32.mouse_event(0x0008, 0, 0, 0, 0)  # MOUSEEVENTF_RIGHTDOWN
                ctypes.windll.user32.mouse_event(0x0010, 0, 0, 0, 0)  # MOUSEEVENTF_RIGHTUP

            # Restore window immediately (no QTimer to avoid threading issues)
            self._restore_window()

        except Exception as e:
            print(f"❌ Error simulating click: {e}")
            self._restore_window()

    def _restore_window(self):
        """Restore window visibility"""
        try:
            self.show()
            self.raise_()
            print("🔧 Window restored after click-through")
        except Exception as e:
            print(f"❌ Error restoring window: {e}")

    def restore_window_flags(self, original_flags):
        """Restore original window flags after click-through"""
        try:
            self.setWindowFlags(original_flags)
            self.show()  # Refresh window with restored flags
            print("� Window flags restored after click-through")
        except Exception as e:
            print(f"❌ Error restoring window flags: {e}")

    def response_area_enter_event(self, event):
        """Handle mouse enter events for response area - maintain arrow cursor"""
        try:
            # Force arrow cursor on both widget and viewport when mouse enters
            self.response_area.setCursor(Qt.ArrowCursor)
            self.response_area.viewport().setCursor(Qt.ArrowCursor)

            # Ensure no focus
            if self.response_area.hasFocus():
                self.response_area.clearFocus()

            print("🖱️ Mouse entered response area - cursor set to arrow")
        except Exception as e:
            print(f"❌ Error in response area enter event: {e}")

    def response_area_leave_event(self, event):
        """Handle mouse leave events for response area"""
        try:
            # Ensure cursor remains arrow when leaving (in case it gets reset)
            self.response_area.setCursor(Qt.ArrowCursor)
            self.response_area.viewport().setCursor(Qt.ArrowCursor)
            print("🖱️ Mouse left response area")
        except Exception as e:
            print(f"❌ Error in response area leave event: {e}")

    def enforce_cursor_behavior(self):
        """Periodically enforce arrow cursor behavior on response area"""
        try:
            if hasattr(self, 'response_area') and self.response_area:
                # Always force arrow cursor on both widget and viewport
                self.response_area.setCursor(Qt.ArrowCursor)
                self.response_area.viewport().setCursor(Qt.ArrowCursor)

                # Always ensure no focus to prevent blinking
                if self.response_area.hasFocus():
                    self.response_area.clearFocus()

                # Ensure text interaction is disabled
                self.response_area.setTextInteractionFlags(Qt.NoTextInteraction)

                # Ensure focus policy is maintained
                self.response_area.setFocusPolicy(Qt.NoFocus)

        except Exception as e:
            # Silently handle errors to avoid spam
            pass

    def check_user_visibility(self):
        """CRITICAL: Periodically check and ensure user can see the application"""
        try:
            # Check if window opacity is too low (invisible to user)
            current_opacity = self.windowOpacity()
            if current_opacity < 0.5:  # If less than 50% visible
                print(f"⚠️ CRITICAL: Window opacity too low ({current_opacity}) - fixing for user visibility")
                self.setWindowOpacity(1.0)  # Make fully visible

            # Ensure window is still visible
            if not self.isVisible():
                print("⚠️ CRITICAL: Window became hidden - showing for user")
                self.show()
                self.raise_()

            # Ensure input field is visible when in input mode
            if hasattr(self, 'question_input') and self.is_input_mode:
                if not self.question_input.isVisible():
                    print("⚠️ CRITICAL: Input field became hidden - showing for user")
                    self.question_input.setVisible(True)

        except Exception as e:
            print(f"❌ Error in visibility check: {e}")
            # Emergency fallback
            try:
                self.setWindowOpacity(1.0)
                self.show()
            except:
                pass

    def set_response_area_transparency(self, transparency_value):
        """Set response area transparency - use this to adjust transparency at runtime

        Args:
            transparency_value (float): 0.1 = very transparent, 0.9 = almost opaque
        """
        try:
            global RESPONSE_AREA_TRANSPARENCY
            RESPONSE_AREA_TRANSPARENCY = max(0.1, min(0.9, transparency_value))

            # Apply the new transparency to response area
            if hasattr(self, 'response_area') and self.response_area:
                self.response_area.setWindowOpacity(RESPONSE_AREA_TRANSPARENCY)

            print(f"🔧 Response area transparency set to: {RESPONSE_AREA_TRANSPARENCY}")

        except Exception as e:
            print(f"❌ Error setting transparency: {e}")

    def get_current_transparency(self):
        """Get current transparency value"""
        return RESPONSE_AREA_TRANSPARENCY

    def make_fully_visible(self):
        """Make window fully visible to user"""
        self.setWindowOpacity(1.0)
        print("👁️ Window is now FULLY VISIBLE to you!")

    def emergency_hide(self):
        """Emergency hide for interviews"""
        self.setWindowOpacity(0.1)
        print("🚨 Emergency hide activated!")

    def restore_visibility(self):
        """Restore normal visibility"""
        self.setWindowOpacity(1.0)
        print("✅ Normal visibility restored!")

    def make_more_visible(self):
        """Make window more visible for user"""
        self.setWindowOpacity(0.9)
        print("👁️ Window made more visible (90% opacity)")

    def make_less_visible(self):
        """Make window less visible for stealth"""
        self.setWindowOpacity(0.3)
        print("🥷 Window made less visible (30% opacity)")

    def set_custom_opacity(self, opacity):
        """Set custom opacity (0.1 to 1.0)"""
        opacity = max(0.1, min(1.0, opacity))
        self.setWindowOpacity(opacity)
        print(f"🔧 Window opacity set to {opacity}")

    def toggle_visibility(self):
        """Toggle between visible and stealth mode"""
        current_opacity = self.windowOpacity()
        if current_opacity > 0.5:
            self.make_less_visible()
        else:
            self.make_more_visible()

    def toggle_window_visibility(self):
        """Toggle window visibility using Ctrl+Space - hide/show window completely"""
        print("🔄 Toggling window visibility...")

        # CRITICAL FIX: Add safeguards to prevent application closure
        try:
            if not self.current_user:
                print("❌ Access denied - User not authenticated")
                return

            # CRITICAL: Ensure we don't accidentally close the application
            if not hasattr(self, 'isVisible') or not hasattr(self, 'windowState'):
                print("❌ Window methods not available - aborting toggle")
                return

            current_state = self.windowState()
            is_visible = self.isVisible()
            print(f"🔄 Current window state: {current_state}, visible: {is_visible}")

            # FIXED: Use safer window state management
            if current_state != Qt.WindowMinimized and is_visible:
                # Minimize the window (better than hide for keyboard listener)
                print("🔄 Minimizing window...")
                self.user_intentionally_hidden = True  # Mark as intentionally hidden

                # CRITICAL FIX: Use QTimer to safely minimize in next event loop
                QTimer.singleShot(0, lambda: self._safe_minimize_window())
                print("🔄 Ctrl+Space: Window minimize scheduled")
            else:
                # Restore the window
                print("🔄 Restoring window...")
                self.user_intentionally_hidden = False  # Mark as not hidden

                # CRITICAL FIX: Use QTimer to safely restore in next event loop
                QTimer.singleShot(0, lambda: self._safe_restore_window())
                print("🔄 Ctrl+Space: Window restore scheduled")

            print("🔄 Toggle visibility completed successfully")

        except Exception as e:
            print(f"❌ CRITICAL ERROR in toggle_window_visibility: {e}")
            import traceback
            traceback.print_exc()
            # CRITICAL: Don't let exceptions propagate and crash the app
            print("🔄 Toggle visibility failed but application continues running")

    def _safe_minimize_window(self):
        """Safely minimize window in main thread"""
        try:
            print("🔄 Executing safe window minimize...")
            self.setWindowState(Qt.WindowMinimized)
            print("✅ Window minimized successfully")
        except Exception as e:
            print(f"❌ Error in safe minimize: {e}")

    def _safe_restore_window(self):
        """Safely restore window in main thread"""
        try:
            print("🔄 Executing safe window restore...")
            self.setWindowState(Qt.WindowNoState)
            self.show()
            self.raise_()
            self.activateWindow()
            print("✅ Window restored successfully")
        except Exception as e:
            print(f"❌ Error in safe restore: {e}")

    def _emit_toggle_signal_safely(self):
        """Safely emit toggle visibility signal in main thread"""
        try:
            print("🔄 Safely emitting toggle visibility signal...")
            if hasattr(self, 'keyboard_signals') and self.keyboard_signals:
                self.keyboard_signals.toggle_visibility_signal.emit()
                print("✅ Toggle signal emitted successfully")
            else:
                print("❌ Keyboard signals not available for emission")
        except Exception as e:
            print(f"❌ CRITICAL ERROR in safe signal emission: {e}")
            import traceback
            traceback.print_exc()
            # CRITICAL: Don't let this crash the application
            print("🔄 Signal emission failed but application continues")

class LoginDialog(QDialog):
    """Login dialog for user authentication"""

    def __init__(self, firebase_manager):
        super().__init__()
        self.firebase_manager = firebase_manager
        self.user_data = None
        self.setup_ui()

        # CRITICAL FIX: COMPLETELY DISABLE stealth mode for login dialog
        # Input fields must work normally - no stealth interference
        print("🔧 STEALTH DISABLED for login dialog - inputs must work!")

    def setup_ui(self):
        """Setup login dialog UI - Email only"""
        self.setWindowTitle("🔐 Login - Abid Ansari AI Assistant")
        self.setFixedSize(400, 250)
        self.setModal(True)

        layout = QVBoxLayout()

        # Title
        title = QLabel("🔐 Login to AI Assistant")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)

        # Info message - Production version
        info = QLabel(f"Enter your registered email address to access {PRODUCTION_CONFIG['app_name']}\nNo password required - secure email-only authentication")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 20px;")
        layout.addWidget(info)

        # Email input - SIMPLIFIED for maximum compatibility
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("📧 Enter your email")
        self.email_input.setStyleSheet("padding: 10px; font-size: 14px; border: 2px solid #ddd; border-radius: 5px;")

        # Add Ctrl+Backspace support for email input
        def email_key_handler(event):
            from PyQt5.QtCore import Qt
            if event.key() == Qt.Key_Backspace and event.modifiers() & Qt.ControlModifier:
                self.email_input.clear()
                print("🗑️ Ctrl+Backspace: Email field cleared")
            else:
                QLineEdit.keyPressEvent(self.email_input, event)

        self.email_input.keyPressEvent = email_key_handler
        layout.addWidget(self.email_input)

        # Buttons - FIXED: Only Login and Cancel
        button_layout = QHBoxLayout()

        self.login_btn = QPushButton("🔐 Login")
        self.login_btn.clicked.connect(self.login)
        self.login_btn.setStyleSheet("padding: 10px; font-size: 14px; background: #4CAF50; color: white; border: none; border-radius: 5px;")

        self.cancel_btn = QPushButton("❌ Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setStyleSheet("padding: 10px; font-size: 14px; background: #757575; color: white; border: none; border-radius: 5px;")

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.login_btn)
        layout.addLayout(button_layout)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; margin: 10px;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def apply_stealth_after_ui_setup(self):
        """Apply stealth mode after UI is fully setup to preserve styling"""
        try:
            # CRITICAL: Ensure dialog is fully visible and functional to user
            self.setWindowOpacity(1.0)
            self.setAttribute(Qt.WA_OpaquePaintEvent)
            self.setEnabled(True)  # Ensure dialog is enabled

            # CRITICAL: Ensure all input fields are fully functional
            for widget in [self.email_input]:
                widget.setWindowOpacity(1.0)
                widget.setAttribute(Qt.WA_OpaquePaintEvent)
                widget.setFocusPolicy(Qt.StrongFocus)
                widget.setEnabled(True)  # Explicitly enable input fields
                widget.setAttribute(Qt.WA_TransparentForMouseEvents, False)  # Allow mouse events

            # Only apply stealth hiding via Windows API (no Qt attributes)
            if STEALTH_MODE_AVAILABLE:
                stealth_manager.hide_from_screen_capture(self)
                stealth_manager.hide_from_taskbar(self)

                print("🥷 Applied MINIMAL stealth to login dialog (inputs fully functional)")
        except Exception as e:
            print(f"❌ Error applying stealth to login dialog: {e}")

    def login(self):
        """Handle login with email only"""
        email = self.email_input.text().strip()

        if not email:
            self.status_label.setText("Please enter your email address")
            return

        if "@" not in email or "." not in email:
            self.status_label.setText("Please enter a valid email address")
            return

        # Authenticate with Firebase using email only
        success, message, user_data = self.firebase_manager.authenticate_user_by_email(email)

        if success:
            self.user_data = user_data
            self.accept()
        else:
            self.status_label.setText(message)



class CreateAccountDialog(QDialog):
    """Create account dialog"""

    def __init__(self, firebase_manager):
        super().__init__()
        self.firebase_manager = firebase_manager
        self.user_data = None
        self.setup_ui()

        # CRITICAL FIX: COMPLETELY DISABLE stealth mode for create account dialog
        # Input fields must work normally - no stealth interference
        print("🔧 STEALTH DISABLED for create account dialog - inputs must work!")

    def setup_ui(self):
        """Setup create account dialog UI"""
        self.setWindowTitle("🆕 Create Account - Abid Ansari AI Assistant")
        self.setFixedSize(400, 400)
        self.setModal(True)

        layout = QVBoxLayout()

        # Title
        title = QLabel("🆕 Create New Account")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title)

        # Info
        info = QLabel("Get 5 minutes free access!")
        info.setAlignment(Qt.AlignCenter)
        info.setStyleSheet("color: #4CAF50; font-size: 14px; margin-bottom: 20px;")
        layout.addWidget(info)

        # Input fields - SIMPLIFIED for maximum compatibility
        input_style = "padding: 10px; font-size: 14px; border: 2px solid #ddd; border-radius: 5px;"

        # Email input with Ctrl+Backspace support
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("📧 Enter your email")
        self.email_input.setStyleSheet(input_style)

        def email_key_handler(event):
            from PyQt5.QtCore import Qt
            if event.key() == Qt.Key_Backspace and event.modifiers() & Qt.ControlModifier:
                self.email_input.clear()
                print("🗑️ Ctrl+Backspace: Email field cleared")
            else:
                QLineEdit.keyPressEvent(self.email_input, event)

        self.email_input.keyPressEvent = email_key_handler
        layout.addWidget(self.email_input)

        # Password input with Ctrl+Backspace support
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("🔒 Enter your password (min 6 characters)")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(input_style)

        def password_key_handler(event):
            from PyQt5.QtCore import Qt
            if event.key() == Qt.Key_Backspace and event.modifiers() & Qt.ControlModifier:
                self.password_input.clear()
                print("🗑️ Ctrl+Backspace: Password field cleared")
            else:
                QLineEdit.keyPressEvent(self.password_input, event)

        self.password_input.keyPressEvent = password_key_handler
        layout.addWidget(self.password_input)

        # Confirm password input with Ctrl+Backspace support
        self.confirm_password_input = QLineEdit()
        self.confirm_password_input.setPlaceholderText("🔒 Confirm your password")
        self.confirm_password_input.setEchoMode(QLineEdit.Password)
        self.confirm_password_input.setStyleSheet(input_style)

        def confirm_password_key_handler(event):
            from PyQt5.QtCore import Qt
            if event.key() == Qt.Key_Backspace and event.modifiers() & Qt.ControlModifier:
                self.confirm_password_input.clear()
                print("🗑️ Ctrl+Backspace: Confirm password field cleared")
            else:
                QLineEdit.keyPressEvent(self.confirm_password_input, event)

        self.confirm_password_input.keyPressEvent = confirm_password_key_handler
        layout.addWidget(self.confirm_password_input)

        # Buttons
        button_layout = QHBoxLayout()

        self.create_btn = QPushButton("🆕 Create Account")
        self.create_btn.clicked.connect(self.create_account)
        self.create_btn.setStyleSheet("padding: 10px; font-size: 14px; background: #4CAF50; color: white; border: none; border-radius: 5px;")

        self.back_btn = QPushButton("⬅️ Back to Login")
        self.back_btn.clicked.connect(self.reject)
        self.back_btn.setStyleSheet("padding: 10px; font-size: 14px; background: #757575; color: white; border: none; border-radius: 5px;")

        button_layout.addWidget(self.back_btn)
        button_layout.addWidget(self.create_btn)
        layout.addLayout(button_layout)

        # Status label
        self.status_label = QLabel("")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: red; margin: 10px;")
        layout.addWidget(self.status_label)

        self.setLayout(layout)

    def apply_stealth_after_ui_setup(self):
        """Apply stealth mode after UI is fully setup to preserve styling"""
        try:
            # CRITICAL: Ensure dialog is fully visible and functional to user
            self.setWindowOpacity(1.0)
            self.setAttribute(Qt.WA_OpaquePaintEvent)
            self.setEnabled(True)  # Ensure dialog is enabled

            # CRITICAL: Ensure all input fields are fully functional
            for widget in [self.email_input, self.password_input, self.confirm_password_input]:
                widget.setWindowOpacity(1.0)
                widget.setAttribute(Qt.WA_OpaquePaintEvent)
                widget.setFocusPolicy(Qt.StrongFocus)
                widget.setEnabled(True)  # Explicitly enable input fields
                widget.setAttribute(Qt.WA_TransparentForMouseEvents, False)  # Allow mouse events

            # Only apply stealth hiding via Windows API (no Qt attributes)
            if STEALTH_MODE_AVAILABLE:
                stealth_manager.hide_from_screen_capture(self)
                stealth_manager.hide_from_taskbar(self)

                print("🥷 Applied MINIMAL stealth to create account dialog (inputs fully functional)")
        except Exception as e:
            print(f"❌ Error applying stealth to create account dialog: {e}")

    def create_account(self):
        """Handle account creation"""
        email = self.email_input.text().strip()
        password = self.password_input.text().strip()
        confirm_password = self.confirm_password_input.text().strip()

        # Validation
        if not email or not password or not confirm_password:
            self.status_label.setText("Please fill all fields")
            return

        if "@" not in email or "." not in email:
            self.status_label.setText(ERROR_MESSAGES.get("invalid_email", "Invalid email"))
            return

        if len(password) < 6:
            self.status_label.setText(ERROR_MESSAGES.get("password_too_short", "Password too short"))
            return

        if password != confirm_password:
            self.status_label.setText(ERROR_MESSAGES.get("passwords_dont_match", "Passwords don't match"))
            return

        # Get device ID
        device_id = get_device_id()

        # Create account in Firebase
        success, message = self.firebase_manager.create_user(email, password, device_id)

        if success:
            # Auto-login after creation
            success, login_message, user_data = self.firebase_manager.authenticate_user(email, password, device_id)
            if success:
                self.user_data = user_data
                self.accept()
            else:
                self.status_label.setText("Account created but login failed")
        else:
            self.status_label.setText(message)

def main():
    """Main application entry point with stealth mode"""
    try:
        # Create QApplication with stealth settings
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(False)

        # Set application properties
        app.setApplicationName("Abid Ansari AI Assistant")
        app.setApplicationVersion("1.0")

        # Apply stealth mode to application
        if STEALTH_MODE_AVAILABLE:
            # Hide from taskbar at application level
            app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)
            print("🥷 Application-level stealth mode enabled")

        # Create main popup (will be hidden initially until authentication)
        popup = MainPopup()

        # FIXED: Don't show popup here - it will be shown after authentication
        print("🔐 Application created - waiting for authentication")
        if STEALTH_MODE_AVAILABLE:
            print("🥷 Application prepared in STEALTH MODE")
            print("🥷 Window will be HIDDEN from:")
            print("   ✅ Taskbar")
            print("   ✅ Screen sharing/recording")
            print("   ✅ Online meetings (Zoom, Teams, etc.)")
            print("   ✅ All external visibility")
            print("👁️ Only YOU will see it after login!")
        else:
            print("👁️ Application prepared in NORMAL MODE")

        print("\n🤖 Abid Ansari AI Assistant Started!")
        print("📋 Features:")
        print("   • Press Caps Lock to ask questions (auto-populates clipboard text)")
        print("   • Enter to submit, Shift+Enter for new line")
        print("   • Backspace to delete characters, Ctrl+Backspace to clear all")
        print("   • Alt key for 300px screenshot around cursor")
        print("   • Delete button to remove screenshots")
        print("   • Shift + Arrow keys for window navigation")
        print("   • Firebase authentication required")
        print("   • Multiple AI models: Gemini → Mistral → OpenRouter → OpenAI")

        if STEALTH_MODE_AVAILABLE:
            print("🥷 STEALTH MODE ACTIVE:")
            print(f"   • Response area transparency: {RESPONSE_AREA_TRANSPARENCY}")
            print(f"   • Click-through duration: {CLICK_THROUGH_DURATION}ms")
            print("   • Hidden from taskbar: ✅")
            print("   • Hidden from screen sharing: ✅")
            print("   • Hidden from online meetings: ✅")
            print("   • All popups hidden: ✅")
        else:
            print("🔧 Basic Configuration:")
            print(f"   • Response area transparency: {RESPONSE_AREA_TRANSPARENCY}")
            print(f"   • Click-through duration: {CLICK_THROUGH_DURATION}ms")
            print("   • Cursor behavior: Always arrow cursor")

        # Start the application
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
