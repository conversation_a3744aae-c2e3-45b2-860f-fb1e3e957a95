('D:\\ABID\\ANSARI AI\\ABID_AI_V2_WITH_LOGIN\\dist\\AbidAnsariAI.exe',
 False,
 False,
 False,
 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 <PERSON>als<PERSON>,
 <PERSON>alse,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'D:\\ABID\\ANSARI '
 'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\AbidAnsariAI.pkg',
 [('O', None, 'OPTION'),
  ('O', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('abidansari',
   'D:\\ABID\\ANSARI AI\\ABID_AI_V2_WITH_LOGIN\\abidansari.py',
   'PYSOURCE-2'),
  ('python312.dll',
   'C:\\Program Files (x86)\\Python312-32\\python312.dll',
   'BINARY'),
  ('_sounddevice_data\\portaudio-binaries\\libportaudio32bit-asio.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\_sounddevice_data\\portaudio-binaries\\libportaudio32bit-asio.dll',
   'BINARY'),
  ('_sounddevice_data\\portaudio-binaries\\libportaudio32bit.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\_sounddevice_data\\portaudio-binaries\\libportaudio32bit.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('grpc\\_cython\\cygrpc.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\grpc\\_cython\\cygrpc.cp312-win32.pyd',
   'EXTENSION'),
  ('google\\_upb\\_message.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google\\_upb\\_message.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\_cffi_backend.cp312-win32.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_webp.cp312-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_imagingtk.cp312-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_imagingcms.cp312-win32.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_imagingmath.cp312-win32.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_imaging.cp312-win32.pyd',
   'EXTENSION'),
  ('msgpack\\_cmsgpack.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\msgpack\\_cmsgpack.cp312-win32.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\pydantic_core\\_pydantic_core.cp312-win32.pyd',
   'EXTENSION'),
  ('_zoneinfo.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_zoneinfo.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PIL\\_imagingft.cp312-win32.pyd',
   'EXTENSION'),
  ('jiter\\jiter.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\jiter\\jiter.cp312-win32.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\sip.cp312-win32.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\charset_normalizer\\md__mypyc.cp312-win32.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp312-win32.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\charset_normalizer\\md.cp312-win32.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files (x86)\\Python312-32\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program Files (x86)\\Python312-32\\python3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files (x86)\\Python312-32\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('grpc\\_cython\\_credentials\\roots.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\grpc\\_cython\\_credentials\\roots.pem',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\METADATA',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\LICENSE',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\top_level.txt',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\INSTALLER',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\RECORD',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_core-2.24.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\METADATA',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\REQUESTED',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-45.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\cryptography-45.0.2.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Marigot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chongqing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Magadan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Athens',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vienna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Aleutian',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Sakhalin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Choibalsan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Khandyga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Queensland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Nauru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\W-SU',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\W-SU',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+4',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Syowa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cuiaba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Adelaide',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Martinique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kashgar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tunis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tashkent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Copenhagen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\ACT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Noumea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Barbados',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Katmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Volgograd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Niue',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Colombo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\LHI',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aden',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Halifax',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jayapura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vientiane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chatham',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('tzdata\\zoneinfo\\Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Timbuktu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('tzdata\\zoneinfo\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+7',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Blantyre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Stockholm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Brisbane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Monrovia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Simferopol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Manaus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dushanbe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('tzdata\\zoneinfo\\GB',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GB',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Faroe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boa_Vista',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vaduz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\NZ',
   'DATA'),
  ('tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Budapest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Johns',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Auckland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tallinn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Singapore',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Midway',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('tzdata\\zoneinfo\\CST6CDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\CST6CDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tarawa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('tzdata\\zoneinfo\\zonenow.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\NZ-CHAT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sao_Paulo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mauritius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bogota',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-14',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Galapagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Tripoli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Navajo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Navajo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chicago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Amsterdam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Algiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Abidjan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kabul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Shiprock',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Banjul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Christmas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Currie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Winnipeg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Funafuti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tehran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Berlin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dhaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-5',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Curacao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guatemala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Danmarkshavn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tegucigalpa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mbabane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Astrakhan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Chagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Atyrau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bangkok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Urumqi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('tzdata\\zoneinfo\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pontianak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Manila',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kosrae',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Beirut',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Marquesas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Nelson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tirane',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Melbourne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuwait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('tzdata\\zoneinfo\\Iran',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Iran',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Gibraltar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Djibouti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Johannesburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santarem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Antigua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('tzdata\\zoneinfo\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Pangnirtung',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rosario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Tiraspol',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Asuncion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guadeloupe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Aruba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bujumbura',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ojinaga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Samarkand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Moncton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Warsaw',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Noronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Ljubljana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tbilisi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Goose_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bamako',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Arizona',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Anadyr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Enderbury',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kampala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Vancouver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\MST7MDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\MST7MDT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guyana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Gaborone',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Hobart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mexico_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baghdad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Saipan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Istanbul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Mogadishu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Aqtobe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\EasterIsland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Toronto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-10',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Maceio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santiago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Baku',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Monterrey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('tzdata\\zoneinfo\\EST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\EST',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Almaty',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maseru',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yakutat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Bougainville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Barthelemy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Douala',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Moscow',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('tzdata\\zoneinfo\\UTC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\UTC',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Honolulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Guernsey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Ponape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-13',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Samara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Pyongyang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Karachi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Brussels',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anchorage',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bucharest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Edmonton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('tzdata\\zoneinfo\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Recife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Recife',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Iqaluit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Majuro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('tzdata\\zoneinfo\\Hongkong',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Hongkong',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\Jamaica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Jamaica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Creston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Creston',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dubai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Thomas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Denver',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Denver',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Eucla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Accra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Caracas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Mawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ndjamena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Norfolk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Apia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Merida',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Merida',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kolkata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yangon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dakar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Newfoundland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cayenne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Miquelon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Vladivostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Araguaina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Luanda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kiev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Podgorica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Rangoon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atikokan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Vostok',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('tzdata\\zoneinfo\\zone1970.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\East',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Harbin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Davis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chita',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Metlakatla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Maputo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\East-Indiana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fortaleza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Yukon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qatar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belfast',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Yap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Oral',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macao',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Resolute',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santo_Domingo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Seoul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ceuta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\New_York',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\New_York',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-8',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Busingen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('tzdata\\zoneinfo\\MST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\MST',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bahrain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Belem',
   'DATA'),
  ('tzdata\\zoneinfo\\PRC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\PRC',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Michigan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Luxembourg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Scoresbysund',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kyiv',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Juneau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montreal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmara',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Kitts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('tzdata\\zoneinfo\\Iceland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Iceland',
   'DATA'),
  ('tzdata\\zoneinfo\\GB-Eire',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GB-Eire',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lusaka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Omsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Kerguelen',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('tzdata\\zoneinfo\\zone.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\zone.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Indiana-Starke',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kathmandu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimbu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\North',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\North',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Riyadh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Canberra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Kanton',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Famagusta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Vincent',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Victoria',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Malabo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Riga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Brazzaville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\General',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port_of_Spain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yerevan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Bratislava',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Yancowinna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('tzdata\\zoneinfo\\iso3166.tab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Swift_Current',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vilnius',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Freetown',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\St_Lucia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('tzdata\\zoneinfo\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Pacific',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Louisville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Porto_Velho',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Glace_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('tzdata\\zoneinfo\\Factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Factory',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Knox_IN',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashgabat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\NSW',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Havana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Havana',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kinshasa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-9',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('tzdata\\zoneinfo\\EET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\EET',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Irkutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Turkey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Turkey',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kuching',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coyhaique',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Easter',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yakutsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Calcutta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Atlantic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('tzdata\\zoneinfo\\PST8PDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\PST8PDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Juba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Grand_Turk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\El_Salvador',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tokyo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Regina',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Regina',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hovd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('tzdata\\zoneinfo\\Egypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Egypt',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Cocos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Sydney',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tortola',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jakarta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Jersey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Saigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Tomsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Thimphu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Adak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Adak',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sofia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wallis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Kralendijk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Azores',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ensenada',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Skopje',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Nome',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zagreb',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Tahiti',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dominica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Managua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Managua',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Canary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('tzdata\\zoneinfo\\Israel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Israel',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qostanay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Niamey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Makassar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lagos',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Eirunepe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Alaska',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Macau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Amman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Zurich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bissau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Maldives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Monaco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Belgrade',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Yellowknife',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nairobi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Helsinki',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dili',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Tijuana',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Saratov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Samoa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Cairo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Vatican',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Gambier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Kirov',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Central',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Boise',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Boise',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Libreville',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Taipei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Belize',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Belize',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Lisbon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Whitehorse',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Minsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nipigon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Rothera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Wake',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Universal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Stanley',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Fort_Wayne',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Inuvik',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Dublin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('tzdata\\zoneinfo\\Cuba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Cuba',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thunder_Bay',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Palmer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('tzdata\\zoneinfo\\tzdata.zi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Sarajevo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Coral_Harbour',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mahe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('tzdata\\zoneinfo\\EST5EDT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\EST5EDT',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Conakry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Perth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Punta_Arenas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Mayotte',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Guayaquil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Kigali',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Bangui',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Greenwich',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Troll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Dacca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\South',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\South',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Harare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Mariehamn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\London',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\London',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Andorra',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Khartoum',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Muscat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Antananarivo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lower_Princes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Mountain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Hermosillo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('tzdata\\zoneinfo\\Chile\\Continental',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Phoenix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Sitka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson_Creek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('tzdata\\zoneinfo\\HST',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\HST',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Windhoek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Efate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Chihuahua',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('tzdata\\zones',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zones',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Damascus',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Cancun',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Matamoros',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('tzdata\\zoneinfo\\Kwajalein',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Kwajalein',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\La_Paz',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Costa_Rica',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('tzdata\\zoneinfo\\Portugal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Portugal',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mendoza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Gaza',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GMT',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Chisinau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('tzdata\\zoneinfo\\ROK',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\ROK',
   'DATA'),
  ('tzdata\\zoneinfo\\Mexico\\BajaSur',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Virgin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Paris',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Los_Angeles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Fiji',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Thule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Thule',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Chuuk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Asmera',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Nicosia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Truk',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\Acre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Darwin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('tzdata\\zoneinfo\\Poland',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Poland',
   'DATA'),
  ('tzdata\\zoneinfo\\Japan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Japan',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('tzdata\\zoneinfo\\ROC',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\ROC',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rainy_River',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Palau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Lindeman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Anguilla',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Santa_Isabel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('tzdata\\zoneinfo\\Canada\\Eastern',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GMT0',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('tzdata\\zoneinfo\\Brazil\\West',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Port-au-Prince',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Nouakchott',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('tzdata\\zoneinfo\\MET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\MET',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Casablanca',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Shanghai',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Atka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Atka',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Malta',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Madeira',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Tasmania',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('tzdata\\zoneinfo\\US\\Hawaii',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Oslo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Rio_Branco',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Lome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('tzdata\\zoneinfo\\WET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\WET',
   'DATA'),
  ('tzdata\\zoneinfo\\leapseconds',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\leapseconds',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Mazatlan',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Dawson',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Barnaul',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montserrat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Kamchatka',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\UCT',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Montevideo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Panama',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Panama',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Paramaribo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Puerto_Rico',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Rome',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Comoro',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('tzdata\\zoneinfo\\Indian\\Reunion',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Brunei',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT-0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GMT-0',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\Casey',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Jerusalem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Bishkek',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Chungking',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Godthab',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Detroit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Nassau',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Madrid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('tzdata\\zoneinfo\\Libya',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Libya',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Menominee',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\Prague',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT-11',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Lima',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Lima',
   'DATA'),
  ('tzdata\\zoneinfo\\Asia\\Hebron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\GMT+6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('tzdata\\zoneinfo\\CET',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\CET',
   'DATA'),
  ('tzdata\\zoneinfo\\GMT+0',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\GMT+0',
   'DATA'),
  ('tzdata\\zoneinfo\\Etc\\Zulu',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('tzdata\\zoneinfo\\America\\Campo_Grande',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('tzdata\\zoneinfo\\Pacific\\Johnston',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('tzdata\\zoneinfo\\Europe\\San_Marino',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\tzdata\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('_sounddevice_data\\portaudio-binaries\\README.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\_sounddevice_data\\portaudio-binaries\\README.md',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.ordertracking_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.ordertracking_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\WHEEL',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\top_level.txt',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\index.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\index.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\METADATA',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\RECORD',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\LICENSE',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseapphosting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.issueresolution_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.issueresolution_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apihub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apihub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'DATA'),
  ('google_api_python_client-2.170.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\google_api_python_client-2.170.0.dist-info\\INSTALLER',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'DATA'),
  ('httplib2\\cacerts.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\httplib2\\cacerts.txt',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('base_library.zip',
   'D:\\ABID\\ANSARI '
   'AI\\ABID_AI_V2_WITH_LOGIN\\build\\AbidAnsariAI\\base_library.zip',
   'DATA')],
 [],
 True,
 False,
 1749038875,
 [('runw.exe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312-32\\site-packages\\PyInstaller\\bootloader\\Windows-32bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Program Files (x86)\\Python312-32\\python312.dll')
